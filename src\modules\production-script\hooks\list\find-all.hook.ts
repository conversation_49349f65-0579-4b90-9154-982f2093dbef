"use client";

import { IBaseHookPaginatedReturn } from "@/shared/types/hooks/hook-paginated.type";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { IProductionScriptDto } from "../../types/dtos/find-all.dto";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useQuery } from "@tanstack/react-query";
import { PRODUCTION_SCRIPT_SUBJECTS } from "../../constants/subjects";
import { productionScriptQueryKeys } from "../../constants/query";
import { createGetRequest } from "@/shared/lib/requests";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { PRODUCTION_SCRIPT_ENDPOINTS } from "../../api/endpoints";
import { processQueryResponsePaginated } from "@/shared/lib/query/process-query-response";

export const useFindAllProductionScript = (params: IPaginationParameters): IBaseHookPaginatedReturn<IProductionScriptDto> => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: productionScriptQueryKeys.list({ ...params }),
		queryFn: () => createGetRequest<IResponsePaginated<IProductionScriptDto>>(PRODUCTION_SCRIPT_ENDPOINTS.FIND_ALL(params)),
		enabled: canRead(PRODUCTION_SCRIPT_SUBJECTS.PRODUCTION_SCRIPT),
	});

	return {
		...processQueryResponsePaginated(data, isFetched),
		isLoading,
	};
};
