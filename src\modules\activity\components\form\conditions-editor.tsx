﻿"use client";
import { requiredLabel } from "@/modules/inspection/components/register/tabs/forms/form-item/form";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { FormControl, FormItem, FormLabel } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { cn } from "@/shared/lib/shadcn/utils";
import { closestCenter, DndContext } from "@dnd-kit/core";
import { SortableContext, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Equal, GripVertical, MoreHorizontal, Plus, Trash2 } from "lucide-react";
import React, { useMemo } from "react";
import { type IActivityConditionOperator, type IActivityLogicOperator } from "../../constants/activity";
import { useConditionsEditor } from "../../hooks/conditions/conditions.hook";
import { ErrorSlot } from "./error-description";

// Função para obter ícone do operador
const OperatorIcon = ({ operator }: { operator: IActivityConditionOperator | string }) => {
	switch (operator) {
		case "IGUAL":
			return <Equal className="text-muted-foreground size-3" />;
		case "DIFERENTE":
			return <span className="text-muted-foreground text-xs">≠</span>;
		case "MAIOR_QUE":
			return <span className="text-muted-foreground text-xs">{">"}</span>;
		case "MENOR_QUE":
			return <span className="text-muted-foreground text-xs">{"<"}</span>;
		case "COMO":
			return <span className="text-muted-foreground text-xs">~</span>;
		case "EM":
			return <span className="text-muted-foreground text-xs">∈</span>;
		case "NAO_EM":
			return <span className="text-muted-foreground text-xs">∉</span>;
		default:
			return <MoreHorizontal className="text-muted-foreground size-3" />;
	}
};

// Função para obter label do operador
const getOperatorLabel = (operator: IActivityConditionOperator) => {
	switch (operator) {
		case "IGUAL":
			return "Igual a";
		case "DIFERENTE":
			return "Diferente de";
		case "MAIOR_QUE":
			return "Maior que";
		case "MENOR_QUE":
			return "Menor que";
		case "COMO":
			return "Contém";
		case "EM":
			return "Está em";
		case "NAO_EM":
			return "Não está em";
		default:
			return operator;
	}
};

// Função para obter placeholder do valor
const getValuePlaceholder = (operator: IActivityConditionOperator) => {
	switch (operator) {
		case "EM":
		case "NAO_EM":
			return "valor1, valor2, valor3";
		case "COMO":
			return "texto a buscar";
		case "MAIOR_QUE":
		case "MENOR_QUE":
			return "número ou data";
		default:
			return "valor a comparar";
	}
};

type HandleProps = React.HTMLAttributes<HTMLButtonElement>;

export const ConditionsEditor = () => {
	const { conditions, addCondition, removeCondition, updateCondition, onDragEnd, inferInputType, operatorOptions, logicOptions } = useConditionsEditor();

	const errors = useMemo(
		() =>
			conditions.map((c, i) => ({
				description: !String(c.description ?? "").trim(),
				field: !String(c.field ?? "").trim(),
				operator: !String(c.operator ?? "").trim(),
				value: !String(c.value ?? "").trim(),
				logic: i < conditions.length - 1 ? !(c.logic && c.logic[0]) : false,
			})),
		[conditions],
	);

	return (
		<div className="flex flex-col gap-4">
			{conditions.length === 0 ? (
				<div className="py-8 text-center">
					<p className="text-muted-foreground mb-4 text-sm">Nenhuma condição adicionada ainda.</p>
					<Button type="button" onClick={addCondition} className="mx-auto">
						<Plus className="size-4" /> Adicionar primeira condição
					</Button>
				</div>
			) : (
				<>
					<DndContext collisionDetection={closestCenter} onDragEnd={onDragEnd}>
						<SortableContext items={conditions.map((_, i: number) => String(i))} strategy={verticalListSortingStrategy}>
							<div className="space-y-3">
								{conditions.map((item, index: number) => (
									<React.Fragment key={`frag-${index}`}>
										<SortableRow id={String(index)} className="bg-card rounded-lg border shadow-sm">
											{({ handleProps, setHandleRef }) => (
												<div className="p-4">
													<div className="flex items-start gap-4">
														<button
															type="button"
															aria-label="Arrastar condição"
															className="hover:bg-muted mt-2 flex-shrink-0 cursor-grab rounded p-1 select-none"
															{...handleProps}
															ref={setHandleRef}
														>
															<GripVertical className="text-muted-foreground size-4" />
														</button>

														<div className="flex-1 space-y-4">
															{/* Descrição */}
															<FormItem className="!gap-1">
																<FormLabel>{requiredLabel("Descrição da condição")}</FormLabel>
																<FormControl>
																	<Input
																		placeholder="Ex.: Mostrar campo quando status for ativo"
																		value={item.description}
																		className={cn(
																			"h-9",
																			errors[index]?.description &&
																				"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																		)}
																		onChange={e => updateCondition(index, "description", e.target.value)}
																	/>
																</FormControl>
																<ErrorSlot show={!!errors[index]?.description} message="A descrição é obrigatória." />
															</FormItem>

															{/* Campos da condição */}
															<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
																<FormItem className="!gap-1">
																	<FormLabel className="flex items-center gap-2">
																		{requiredLabel("Campo")}
																		<span className="text-muted-foreground text-xs">(string)</span>
																	</FormLabel>
																	<FormControl>
																		<Input
																			placeholder="nome_do_campo"
																			value={item.field}
																			className={cn(
																				"h-9 font-mono text-sm",
																				errors[index]?.field &&
																					"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																			)}
																			onChange={e => updateCondition(index, "field", e.target.value)}
																		/>
																	</FormControl>
																	<ErrorSlot show={!!errors[index]?.field} message="O campo é obrigatório." />
																</FormItem>

																<FormItem className="!gap-1">
																	<FormLabel className="flex items-center gap-2">
																		{requiredLabel("Operador")}
																		<OperatorIcon operator={item.operator} />
																	</FormLabel>
																	<FormControl>
																		<Select
																			value={item.operator}
																			onValueChange={val =>
																				updateCondition(index, "operator", val as IActivityConditionOperator)
																			}
																		>
																			<SelectTrigger
																				className={cn(
																					"h-9 w-full",
																					errors[index]?.operator &&
																						"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																				)}
																			>
																				<SelectValue placeholder="Selecione..." />
																			</SelectTrigger>
																			<SelectContent>
																				{operatorOptions.map((op: string) => (
																					<SelectItem key={op} value={op} className="flex items-center gap-2">
																						<div className="flex items-center gap-2">
																							<OperatorIcon operator={op as IActivityConditionOperator} />
																							<span>{getOperatorLabel(op as IActivityConditionOperator)}</span>
																						</div>
																					</SelectItem>
																				))}
																			</SelectContent>
																		</Select>
																	</FormControl>
																	<ErrorSlot show={!!errors[index]?.operator} message="O operador é obrigatório." />
																</FormItem>

																<FormItem className="!gap-1">
																	<FormLabel className="flex items-center gap-2">
																		{requiredLabel("Valor")}
																		<span className="text-muted-foreground text-xs">(string)</span>
																	</FormLabel>
																	<FormControl>
																		<Input
																			placeholder={getValuePlaceholder(item.operator)}
																			value={item.value}
																			className={cn(
																				"h-9",
																				errors[index]?.value &&
																					"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																			)}
																			onChange={e => updateCondition(index, "value", e.target.value)}
																		/>
																	</FormControl>
																	<ErrorSlot show={!!errors[index]?.value} message="O valor é obrigatório." />
																</FormItem>
															</div>
														</div>

														<Button
															type="button"
															variant="outline"
															size="icon"
															onClick={() => removeCondition(index)}
															className="hover:bg-destructive hover:text-destructive-foreground mt-2 flex-shrink-0"
															aria-label="Remover condição"
														>
															<Trash2 className="size-4" />
														</Button>
													</div>
												</div>
											)}
										</SortableRow>

										{index < conditions.length - 1 && (
											<Connector
												value={item.logic?.[0] || "NONE"}
												onChange={val => updateCondition(index, "logic", val === "NONE" ? [] : [val as IActivityLogicOperator])}
												options={["NONE", ...logicOptions]}
												invalid={errors[index]?.logic}
											/>
										)}
									</React.Fragment>
								))}
							</div>
						</SortableContext>
					</DndContext>

					<div className="flex justify-center pt-4">
						<Button type="button" variant="outline" onClick={addCondition}>
							<Plus className="size-4" /> Adicionar condição
						</Button>
					</div>
				</>
			)}
		</div>
	);
};

function SortableRow({
	id,
	className,
	children,
}: {
	id: string;
	className?: string;
	children: (args: { handleProps: HandleProps; setHandleRef: (el: HTMLElement | null) => void }) => React.ReactNode;
}) {
	const { setNodeRef, setActivatorNodeRef, transform, transition, isDragging, attributes, listeners } = useSortable({
		id,
		transition: {
			duration: 200,
			easing: "cubic-bezier(0.25, 1, 0.5, 1)",
		},
	});

	const style: React.CSSProperties = {
		transform: CSS.Transform.toString(transform),
		transition: isDragging ? "none" : transition,
		opacity: isDragging ? 0.6 : 1,
		scale: isDragging ? "1.02" : "1",
		zIndex: isDragging ? 1000 : "auto",
	};

	const handleProps = { ...(attributes as unknown as HandleProps), ...(listeners as unknown as HandleProps) };

	return (
		<div ref={setNodeRef} style={style} className={cn(className, isDragging && "ring-primary/20 shadow-lg ring-2")}>
			{children({ handleProps, setHandleRef: setActivatorNodeRef })}
		</div>
	);
}

function Connector({ value, onChange, options, invalid }: { value: string; onChange: (value: string) => void; options: string[]; invalid?: boolean }) {
	return (
		<div className="relative my-1 flex items-center gap-3 px-2">
			<div className="border-muted-foreground/30 -mr-1 h-0.5 flex-1 border-t border-dashed" />
			<Select value={value} onValueChange={onChange}>
				<SelectTrigger
					className={cn(
						"bg-background/80 ring-border/70 hover:ring-primary/30 focus:ring-primary/30 mx-1 h-8 w-[110px] justify-center px-3 text-xs shadow-sm ring-1 focus:ring-2",
						invalid && "border-destructive text-destructive ring-destructive/40",
					)}
				>
					<SelectValue placeholder="Operador" />
				</SelectTrigger>
				<SelectContent align="center">
					{options.map(op => (
						<SelectItem key={op} value={op}>
							{op === "NONE" ? "—" : op}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
			<div className="border-muted-foreground/30 -ml-1 h-0.5 flex-1 border-t border-dashed" />
		</div>
	);
}
