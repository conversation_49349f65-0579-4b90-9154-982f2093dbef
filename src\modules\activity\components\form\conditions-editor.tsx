﻿"use client";
import { requiredLabel } from "@/modules/inspection/components/register/tabs/forms/form-item/form";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { FormControl, FormItem, FormLabel } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { cn } from "@/shared/lib/shadcn/utils";
import { closestCenter, DndContext } from "@dnd-kit/core";
import { SortableContext, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Filter, GripVertical, Plus, Trash2 } from "lucide-react";
import React, { useMemo } from "react";
import { type IActivityConditionOperator, type IActivityLogicOperator } from "../../constants/activity";
import { useConditionsEditor } from "../../hooks/conditions/conditions.hook";
import { ErrorSlot } from "./error-description";
import { Section } from "./section";

type HandleProps = React.HTMLAttributes<HTMLButtonElement>;

export const ConditionsEditor = () => {
	const { conditions, addCondition, removeCondition, updateCondition, onDragEnd, inferInputType, operatorOptions, logicOptions } = useConditionsEditor();

	const errors = useMemo(
		() =>
			conditions.map((c, i) => ({
				description: !String(c.description ?? "").trim(),
				field: !String(c.field ?? "").trim(),
				operator: !String(c.operator ?? "").trim(),
				value: !String(c.value ?? "").trim(),
				logic: i < conditions.length - 1 ? !(c.logic && c.logic[0]) : false,
			})),
		[conditions],
	);

	return (
		<Section
			title="Condições"
			icon={Filter}
			action={
				<Button type="button" size="sm" onClick={addCondition}>
					<Plus className="size-4" /> Adicionar condição
				</Button>
			}
		>
			<div className="flex flex-col gap-4">
				{conditions.length === 0 ? <p className="text-muted-foreground text-center text-sm">Nenhuma condição adicionada ainda.</p> : null}
				<DndContext collisionDetection={closestCenter} onDragEnd={onDragEnd}>
					<SortableContext items={conditions.map((_, i: number) => String(i))} strategy={verticalListSortingStrategy}>
						<div>
							{conditions.map((item, index: number) => (
								<React.Fragment key={`frag-${index}`}>
									<SortableRow id={String(index)} className="bg-card rounded-main border p-2">
										{({ handleProps, setHandleRef }) => (
											<div className="flex items-start gap-4">
												<button
													type="button"
													aria-label="Arrastar condição"
													className="hover:bg-muted mt-2 flex-shrink-0 cursor-grab rounded p-1 select-none"
													{...handleProps}
													ref={setHandleRef}
												>
													<GripVertical className="text-muted-foreground size-4" />
												</button>

												<div className="flex-1">
													<div className="grid grid-cols-1">
														<FormItem className="!gap-1">
															<FormLabel>{requiredLabel("Descrição")}</FormLabel>
															<FormControl>
																<Input
																	placeholder="Ex.: Mostrar campo X quando Y"
																	value={item.description}
																	className={cn(
																		"h-9",
																		errors[index]?.description &&
																			"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																	)}
																	onChange={e => updateCondition(index, "description", e.target.value)}
																/>
															</FormControl>
															<ErrorSlot show={!!errors[index]?.description} message="A descrição é obrigatória." />
														</FormItem>
													</div>

													<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
														<FormItem className="!gap-1">
															<FormLabel>{requiredLabel("Campo")}</FormLabel>
															<FormControl>
																<Input
																	placeholder="Ex.: Mostrar campo X quando Y"
																	value={item.field}
																	className={cn(
																		"h-9",
																		errors[index]?.field &&
																			"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																	)}
																	onChange={e => updateCondition(index, "field", e.target.value)}
																/>
															</FormControl>
															<ErrorSlot show={!!errors[index]?.field} message="O campo é obrigatório." />
														</FormItem>

														<FormItem className="!gap-1">
															<FormLabel>{requiredLabel("Operador")}</FormLabel>
															<FormControl>
																<Select
																	value={item.operator}
																	onValueChange={val => updateCondition(index, "operator", val as IActivityConditionOperator)}
																>
																	<SelectTrigger
																		className={cn(
																			"h-9 w-full",
																			errors[index]?.operator &&
																				"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																		)}
																	>
																		<SelectValue placeholder="Operador" />
																	</SelectTrigger>
																	<SelectContent>
																		{operatorOptions.map((op: string) => (
																			<SelectItem key={op} value={op}>
																				{op}
																			</SelectItem>
																		))}
																	</SelectContent>
																</Select>
															</FormControl>
															<ErrorSlot show={false} message="O operador é obrigatório." />
														</FormItem>

														<FormItem className="!gap-1">
															<FormLabel>{requiredLabel("Valor")}</FormLabel>
															<FormControl>
																<Input
																	type={inferInputType(item.field)}
																	placeholder={
																		item.operator === "EM" || item.operator === "NAO_EM"
																			? "valor1, valor2"
																			: "Valor a comparar"
																	}
																	value={item.value}
																	className={cn(
																		"h-9",
																		errors[index]?.value &&
																			"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																	)}
																	onChange={e => updateCondition(index, "value", e.target.value)}
																/>
															</FormControl>
															<ErrorSlot show={!!errors[index]?.value} message="O valor é obrigatório." />
														</FormItem>
													</div>
												</div>

												<Button
													type="button"
													variant="outline"
													size="icon"
													onClick={() => removeCondition(index)}
													className="hover:bg-destructive hover:text-destructive-foreground mt-2 flex-shrink-0"
													aria-label="Remover condição"
												>
													<Trash2 className="size-4" />
												</Button>
											</div>
										)}
									</SortableRow>

									{index < conditions.length - 1 ? (
										<Connector
											value={item.logic?.[0] || "NONE"}
											onChange={val => updateCondition(index, "logic", val === "NONE" ? [] : [val as IActivityLogicOperator])}
											options={["NONE", ...logicOptions]}
											invalid={errors[index]?.logic}
										/>
									) : null}
								</React.Fragment>
							))}
						</div>
					</SortableContext>
				</DndContext>
			</div>
		</Section>
	);
};

function SortableRow({
	id,
	className,
	children,
}: {
	id: string;
	className?: string;
	children: (args: { handleProps: HandleProps; setHandleRef: (el: HTMLElement | null) => void }) => React.ReactNode;
}) {
	const { setNodeRef, setActivatorNodeRef, transform, transition, isDragging, attributes, listeners } = useSortable({
		id,
		transition: {
			duration: 200,
			easing: "cubic-bezier(0.25, 1, 0.5, 1)",
		},
	});

	const style: React.CSSProperties = {
		transform: CSS.Transform.toString(transform),
		transition: isDragging ? "none" : transition,
		opacity: isDragging ? 0.6 : 1,
		scale: isDragging ? "1.02" : "1",
		zIndex: isDragging ? 1000 : "auto",
	};

	const handleProps = { ...(attributes as unknown as HandleProps), ...(listeners as unknown as HandleProps) };

	return (
		<div ref={setNodeRef} style={style} className={cn(className, isDragging && "ring-primary/20 shadow-lg ring-2")}>
			{children({ handleProps, setHandleRef: setActivatorNodeRef })}
		</div>
	);
}

function Connector({ value, onChange, options, invalid }: { value: string; onChange: (value: string) => void; options: string[]; invalid?: boolean }) {
	return (
		<div className="relative my-1 flex items-center gap-3 px-2">
			<div className="border-muted-foreground/30 -mr-1 h-0.5 flex-1 border-t border-dashed" />
			<Select value={value} onValueChange={onChange}>
				<SelectTrigger
					className={cn(
						"bg-background/80 ring-border/70 hover:ring-primary/30 focus:ring-primary/30 mx-1 h-8 w-[110px] justify-center px-3 text-xs shadow-sm ring-1 focus:ring-2",
						invalid && "border-destructive text-destructive ring-destructive/40",
					)}
				>
					<SelectValue placeholder="Operador" />
				</SelectTrigger>
				<SelectContent align="center">
					{options.map(op => (
						<SelectItem key={op} value={op}>
							{op === "NONE" ? "—" : op}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
			<div className="border-muted-foreground/30 -ml-1 h-0.5 flex-1 border-t border-dashed" />
		</div>
	);
}
