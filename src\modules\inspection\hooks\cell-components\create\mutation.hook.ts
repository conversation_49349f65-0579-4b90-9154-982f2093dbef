"use client";

import { CELL_COMPONENTS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { ICreateCellByComponentType } from "@/modules/inspection/types/cell-components/dtos/create.dto";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useCreateCellByComponentTypeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, ICreateCellByComponentType> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, ICreateCellByComponentType>(
		{
			mutationKey: inspectionKeys.cellByComponents.custom("create"),
			endpoint: CELL_COMPONENTS_ENDPOINTS.CREATE,
			subject: INSPECTION_SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_COMPONENT,
			type: "create",
			messages: {
				loading: "Criando célula por componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.cellByComponents,
		},
		{ ...params },
	);

	return { ...mutation };
};
