"use client";

import { INSPECTION_FORMS_LINKS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useDeleteFormLinkMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: inspectionKeys.formsLink.custom("delete"),
			endpoint: (id: string) => INSPECTION_FORMS_LINKS_ENDPOINTS.DELETE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_FORM,
			type: "delete",
			messages: {
				loading: "Excluindo vínculo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.formsLink,
		},
		{ ...params },
	);

	return { ...mutation };
};
