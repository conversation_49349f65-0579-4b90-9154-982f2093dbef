import { TPermissionSubject } from "@/config/permissions";
import { QueryClient, QueryKey, UseQueryOptions } from "@tanstack/react-query";

export type MutationType = "create" | "put" | "patch" | "delete";

export interface MutationConfig<TData = unknown, TVariables = unknown> {
	mutationKey: QueryKey;
	endpoint: string | ((variables: TVariables) => string);
	subject: TPermissionSubject;
	type: MutationType;
	messages: {
		loading: string;
		success?: string | ((data: TData) => string);
		error?: string | ((error: Error) => string);
	};
	queryKeys: {
		invalidateAll: (queryClient: QueryClient) => void;
		custom?: (operation: string) => QueryKey;
	};
	onSuccess?: (data: TData, variables: TVariables) => void;
	onError?: (error: Error, variables: TVariables) => void;
	transformRequest?: (variables: TVariables) => unknown;
	mapResponseToData?: (data: unknown) => TData;
	customPermissionCheck?: (variables: TVariables) => boolean | string;
}

export interface IMutationOptions {
	onClose?: () => void;
	disableToast?: boolean;
	disableInvalidation?: boolean;
	queryOptions?: Partial<UseQueryOptions>;
}

export interface IMutationReturn<TData = unknown, TVariables = unknown> {
	mutate: (variables: TVariables) => Promise<TData>;
	data?: TData;
	isLoading: boolean;
	error: Error | null;
	isSuccess: boolean;
	reset: () => void;
	mutateAsync: (variables: TVariables) => Promise<TData>;
}
