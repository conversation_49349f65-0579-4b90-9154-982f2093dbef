"use client";

import { INSPECTION_FORMS_LINKS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { ICreateFormLinkDto } from "@/modules/inspection/types/form-link/dtos/create.dto";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useCreateFormLinkMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, ICreateFormLinkDto> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, ICreateFormLinkDto>(
		{
			mutationKey: inspectionKeys.formsLink.custom("create"),
			endpoint: INSPECTION_FORMS_LINKS_ENDPOINTS.CREATE,
			subject: INSPECTION_SUBJECTS.INSPECTION_FORM,
			type: "create",
			messages: {
				loading: "Criando vínculo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.formsLink,
		},
		{ ...params },
	);

	return { ...mutation };
};
