import { useFindAllCollaboratorSelect } from "@/modules/inspection/hooks/collaborator-by-sector/create/convert-collab.hook";
import { TCreateCollabSectorSchema } from "@/modules/inspection/validators/collaborator-by-sector/create";
import { useFindAllSector } from "@/modules/sector/hooks/list/find-all.hook";
import { GenericSearchSelect } from "@/shared/components/custom/generic-search-select";
import { Button } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { UseFormReturn } from "react-hook-form";

interface IFormCreateCollabBysectorProps {
	onClose: () => void;
	methods: UseFormReturn<TCreateCollabSectorSchema>;
	onSubmit: (data: TCreateCollabSectorSchema) => void;
	isEdit?: boolean;
}

export default function FormCreateCollabBysector({ onClose, methods, onSubmit, isEdit }: IFormCreateCollabBysectorProps) {
	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
				{!isEdit && (
					<FormField
						control={methods.control}
						name="pin"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Pin</FormLabel>
								<FormControl>
									<Input {...field} autoComplete="off" autoFocus placeholder="Digite o pin (Os 4 primeiros digitos do CPF)" />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				)}

				<FormField
					control={methods.control}
					name="collaborator"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Nome do colaborador</FormLabel>
							<FormControl>
								<GenericSearchSelect
									value={field.value}
									useDataHook={useFindAllCollaboratorSelect}
									onChange={value => {
										field.onChange(value);
									}}
									placeholder="Selecione..."
									searchPlaceholder="Buscar colaborador..."
									loadingText="Carregando..."
									emptyText="Nenhuma colaborador encontrado."
									width="w-full"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={methods.control}
					name="sector"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Setor</FormLabel>
							<FormControl>
								<GenericSearchSelect
									value={field.value}
									useDataHook={useFindAllSector}
									onChange={value => {
										field.onChange(value);
									}}
									placeholder="Selecione..."
									searchPlaceholder="Buscar setor..."
									loadingText="Carregando..."
									emptyText="Nenhuma setor encontrado."
									width="w-full"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="flex justify-end gap-2 pt-4">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button type="submit">Salvar</Button>
				</div>
			</form>
		</Form>
	);
}
