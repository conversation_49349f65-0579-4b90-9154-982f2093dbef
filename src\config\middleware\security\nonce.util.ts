import { NextRequest } from "next/server";

export const NONCE_HEADER = "x-nonce" as const;

export function generateBase64Nonce(length = 16): string {
  const bytes = crypto.getRandomValues(new Uint8Array(length));
  let binary = "";
  for (const b of bytes) binary += String.fromCharCode(b);
  return btoa(binary).replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
}

export function isHtmlDocumentRequest(request: NextRequest): boolean {
  const accept = request.headers.get("accept") || "";
  const secFetchDest = request.headers.get("sec-fetch-dest") || "";
  return secFetchDest === "document" || accept.includes("text/html");
}

