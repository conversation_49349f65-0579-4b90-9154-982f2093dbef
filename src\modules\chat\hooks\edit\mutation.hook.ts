"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";
import { TEditKnowledgeSchema } from "../../validators/edit-knowledge";

type TEditKnowledgeVariables = { id: string; form: TEditKnowledgeSchema };

export const useEditKnowledgeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TEditKnowledgeVariables> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, TEditKnowledgeVariables>(
		{
			mutationKey: chatKeys.custom("edit-knowledge"),
			endpoint: ({ id }) => CHAT_ENDPOINTS.UPDATE_KNOWLEDGE(id),
			subject: "all",
			type: "patch",
			messages: {
				loading: "Atualizando conhecimento...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: chatKeys,
			transformRequest: ({ form }) => form,
		},
		{ ...params },
	);

	return { ...mutation };
};
