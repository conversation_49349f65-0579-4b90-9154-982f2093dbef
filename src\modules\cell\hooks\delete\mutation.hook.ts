"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { CELL_ENDPOINTS } from "../../api/endpoints";
import { cellQueryKeys } from "../../constants/query";
import { CELL_SUBJECTS } from "../../constants/subjects";

export const useDeleteCellMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: cellQueryKeys.custom("delete"),
			endpoint: (id: string) => CELL_ENDPOINTS.DELETE(id),
			subject: CELL_SUBJECTS.CELL,
			type: "delete",
			messages: {
				loading: "Excluindo célula...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: cellQueryKeys,
		},
		{ ...params },
	);

	return {
		...mutation,
	};
};
