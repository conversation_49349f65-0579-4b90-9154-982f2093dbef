"use client";

import { useSet<PERSON><PERSON> } from "jotai";
import type { UseFormReturn } from "react-hook-form";
import { activityConditionsAtom } from "../../atoms/conditions.atom";
import { activityFieldsAtom } from "../../atoms/fields.atom";
import type { TCreateActivity } from "../../validators/create";

export const useActivityFormReset = () => {
	const setFields = useSetAtom(activityFieldsAtom);
	const setConditions = useSetAtom(activityConditionsAtom);

	const resetAll = (form: UseFormReturn<TCreateActivity>) => {
		form.reset({ name: "" } as unknown as TCreateActivity);
		setFields([]);
		setConditions([]);
	};

	return { resetAll };
};
