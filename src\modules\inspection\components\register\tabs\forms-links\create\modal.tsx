import { useCreateFormLinkForm } from "@/modules/inspection/hooks/form-links/create/form.hook";
import { useCreateFormLinkMutation } from "@/modules/inspection/hooks/form-links/create/mutation.hook";
import { InspectionFormsLinkToCreateMapper } from "@/modules/inspection/lib/mappers/forms-link/form-to-create.mapper";
import { TCreateFormLink } from "@/modules/inspection/validators/forms-links/create";
import { Modal } from "@/shared/components/custom/modal";
import { FormCreateFormLink } from "./form";

interface ModalCreateFormLinkProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateFormLink = ({ isOpen, onClose }: ModalCreateFormLinkProps) => {
	const methods = useCreateFormLinkForm();
	const handleClose = () => {
		onClose();
		methods.reset();
	};
	const { mutate } = useCreateFormLinkMutation({ onClose: handleClose });
	const handleSubmit = async (data: TCreateFormLink) => await mutate(InspectionFormsLinkToCreateMapper.map(data));

	return (
		<Modal isOpen={isOpen} onClose={handleClose} title="Vínculo de Formulário">
			<FormCreateFormLink methods={methods} onSubmit={handleSubmit} onClose={handleClose} />
		</Modal>
	);
};
