"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { SECTOR_ENDPOINTS } from "../../api/endpoints";
import { sectorQueryKeys } from "../../constants/query";
import { SECTOR_SUBJECTS } from "../../constants/subjects";

export const useCreateSectorMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, { name: string }> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, { name: string }>(
		{
			mutationKey: sectorQueryKeys.custom("create"),
			endpoint: SECTOR_ENDPOINTS.CREATE,
			subject: SECTOR_SUBJECTS.SECTOR,
			type: "create",

			messages: {
				loading: "C<PERSON>do setor...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: sectorQueryKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
