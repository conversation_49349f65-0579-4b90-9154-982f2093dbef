import { extractLoginParams, fetchBackendLoginUrl, isRedirectResponse, isValidRedirectPath } from "@/core/auth/lib/login-utils";
import { NextRequest, NextResponse } from "next/server";

function getRetryUrl(redirect?: string) {
	const loginUrl = "/routes/login";
	if (redirect && redirect !== "/") return `${loginUrl}?redirect=${encodeURIComponent(redirect)}`;
	return loginUrl;
}

function renderErrorHtml(title: string, message: string, opts?: { status?: number; redirect?: string }) {
	const statusText = opts?.status ? `<p style="color:#6b7280;font-size:12px;margin:8px 0 0">Código do erro: <strong>${opts.status}</strong></p>` : "";
	const retryHref = getRetryUrl(opts?.redirect);
	return `<!doctype html>
<html lang="pt-br">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>${title}</title>
    <style>
      body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,Cantarell,Noto Sans,"Helvetica Neue",Arial,"Apple Color Emoji","Segoe UI Emoji";background:linear-gradient(135deg,#f5f7ff,#ffffff);color:#0f172a}
      .wrap{min-height:100vh;display:flex;align-items:center;justify-content:center;padding:24px}
      .card{max-width:560px;width:100%;background:#ffffffcc;border:1px solid #e5e7eb;border-radius:16px;box-shadow:0 10px 25px rgba(0,0,0,.06);padding:24px}
      h1{margin:0 0 8px;font-size:24px}
      p{margin:0 0 12px;line-height:1.5}
      .actions{margin-top:12px;display:flex;gap:8px}
      .btn{appearance:none;border:1px solid #d1d5db;background:#fff;border-radius:8px;padding:10px 14px;cursor:pointer;text-decoration:none;color:#111827}
      .btn:hover{background:#f9fafb}
    </style>
  </head>
  <body>
    <div class="wrap">
      <div class="card" role="alert" aria-live="assertive">
        <h1>${title}</h1>
        <p>${message}</p>
        ${statusText}
        <div class="actions">
          <a class="btn" href="${retryHref}">Tentar novamente</a>
          <a class="btn" href="/">Ir para início</a>
        </div>
      </div>
    </div>
  </body>
</html>`;
}

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { redirectPath, theme } = await extractLoginParams(new URL(request.url));

		if (!isValidRedirectPath(redirectPath)) {
			const html = renderErrorHtml("Redirecionamento inválido", "O endereço de redirecionamento informado não é válido.", { redirect: redirectPath });
			return new NextResponse(html, { status: 400, headers: { "content-type": "text/html; charset=utf-8" } });
		}

		const backendResponse = await fetchBackendLoginUrl(redirectPath, theme);

		if (isRedirectResponse(backendResponse)) {
			const location = backendResponse.location;
			if (!location) throw new Error("URL de redirecionamento não encontrada");
			return NextResponse.redirect(location, 302);
		}

		let errorType: "backend_error" | "network_error" | "timeout_error" = "backend_error";
		if (backendResponse.status === 0) {
			const errorText = backendResponse.errorText || "";
			if (errorText.includes("timeout") || errorText.includes("Tempo limite")) {
				errorType = "timeout_error";
			} else {
				errorType = "network_error";
			}
		}

		const titleMap: Record<string, string> = {
			backend_error: "Problema no servidor",
			network_error: "Sem conexão",
			timeout_error: "Tempo esgotado",
		};

		const title = titleMap[errorType] || "Problema no servidor";
		const message = backendResponse.errorText || "Erro na comunicação com o servidor de autenticação. Tente novamente.";
		const html = renderErrorHtml(title, message, { status: backendResponse.status, redirect: redirectPath });
		return new NextResponse(html, { status: 502, headers: { "content-type": "text/html; charset=utf-8" } });
	} catch (error) {
		console.error("Erro no redirecionamento para Keycloak:", error);
		const { redirectPath: fallbackRedirectPath } = await extractLoginParams(new URL(request.url));
		const html = renderErrorHtml("Erro de autenticação", "Ocorreu um erro interno ao processar o redirecionamento de autenticação.", {
			redirect: fallbackRedirectPath,
		});
		return new NextResponse(html, { status: 500, headers: { "content-type": "text/html; charset=utf-8" } });
	}
}
