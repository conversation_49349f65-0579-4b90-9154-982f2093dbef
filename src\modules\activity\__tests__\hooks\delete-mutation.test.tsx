"use client";

import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";
import { useDeleteActivityMutation } from "../../hooks/delete/mutation.hook";

jest.mock("../../../../shared/lib/requests", () => ({
	createDeleteRequest: jest.fn(),
}));

jest.mock("../../../../core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canDelete: () => true })),
}));

const mockedCreateDeleteRequest = createDeleteRequest as jest.MockedFunction<typeof createDeleteRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Atividade excluída com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Erro ao excluir atividade" };

describe("useDeleteActivityMutation", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve excluir atividade com sucesso", async () => {
		mockedCreateDeleteRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useDeleteActivityMutation({}), { wrapper });
		await expect(result.current.mutate("1")).resolves.toEqual(mockSuccess);
	});

	it("deve lançar erro ao falhar", async () => {
		mockedCreateDeleteRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useDeleteActivityMutation({}), { wrapper });
		await expect(result.current.mutate("1")).rejects.toThrow("Erro ao excluir atividade");
	});
});
