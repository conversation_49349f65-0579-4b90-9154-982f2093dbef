"use client";

import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";

export const useDeleteMeasureMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: inspectionKeys.measures.custom("delete"),
			endpoint: (id: string) => MEASURES_ENDPOINTS.DELETE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_MEASURE,
			type: "delete",
			messages: {
				loading: "Excluindo medida...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.measures,
		},
		{ ...params },
	);

	return { ...mutation };
};
