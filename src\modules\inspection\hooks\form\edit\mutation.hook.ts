"use client";

import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { IUpdateFormDTO } from "@/modules/inspection/types/forms/dtos/update-form.dto";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

type TUpdateFormVariables = { id: string; form: IUpdateFormDTO };

export const useUpdateFormMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TUpdateFormVariables> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, TUpdateFormVariables>(
		{
			mutationKey: inspectionKeys.forms.custom("update"),
			endpoint: ({ id }) => INSPECTION_FORM_ENDPOINTS.UPDATE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_FORM,
			type: "put",
			messages: {
				loading: "Atualizando formulário...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.forms,
			transformRequest: ({ form }) => form,
		},
		{ ...params },
	);

	return { ...mutation };
};
