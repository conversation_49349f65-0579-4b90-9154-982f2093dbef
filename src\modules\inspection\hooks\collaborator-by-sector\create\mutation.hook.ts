"use client";

import { COLLAB_BY_SECTOR_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { ICreateCollabBySectorDTO } from "@/modules/inspection/types/collaborator-by-sector/create.dto";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useCreateCollabBySectorMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, ICreateCollabBySectorDTO> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, ICreateCollabBySectorDTO>(
		{
			mutationKey: inspectionKeys.collabBysector.custom("create"),
			endpoint: COLLAB_BY_SECTOR_ENDPOINTS.CREATE,
			subject: INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR,
			type: "create",
			messages: {
				loading: "Criando vinculo de colaborador por setor...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.collabBysector,
		},
		{
			...params,
		},
	);

	return { ...mutation };
};
