@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
	/* ===== CORES PRINCIPAIS ===== */
	--primary: #004475;
	--primary-foreground: #ffffff;
	--secondary: #f1f5f9;
	--secondary-foreground: #1e293b;

	/* ===== CORES DE FUNDO ===== */
	--background: #f9f9fa;
	--background-secondary: #eceef2;
	--foreground: #000000;

	/* ===== CORES DE TEXTO ===== */
	--text-primary: #000000;
	--text-secondary: #514d4d;

	/* ===== CORES DE BORDA E INPUT ===== */
	--border: #e0e0e0;
	--input: #e0e0e0;
	--ring: #004475;

	/* ===== CORES DE ACENTO ===== */
	--accent: #f1f5f9;
	--accent-foreground: #1e293b;
	--muted: #f1f5f9;
	--muted-foreground: #64748b;

	/* ===== CORES DE DESTRUIÇÃO ===== */
	--destructive: #ef4444;
	--destructive-foreground: #ffffff;

	/* ===== CORES ESPECÍFICAS ===== */
	--leaf-green-color: #00a03c;

	/* ===== CORES DE CARD (MANTIDAS COMO ESTÃO) ===== */
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.129 0.042 264.695);

	/* ===== CORES DE POPOVER ===== */
	--popover: #ffffff;
	--popover-foreground: #000000;
	--ring-background: #ffffff;

	/* ===== CORES DE HEADER ===== */
	--header-bg-from: #f8fafc;
	--header-bg-via: #ffffff;
	--header-bg-to: #f8fafc;

	/* ===== CORES DE CHART ===== */
	--chart-1: #3b82f6;
	--chart-2: #10b981;
	--chart-3: #f59e0b;
	--chart-4: #ef4444;
	--chart-5: #8b5cf6;

	/* ===== CORES DE SIDEBAR ===== */
	--sidebar: #ffffff;
	--sidebar-foreground: #000000;
	--sidebar-primary: #004475;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #f1f5f9;
	--sidebar-accent-foreground: #1e293b;
	--sidebar-border: #e0e0e0;
	--sidebar-ring: #004475;

	/* ===== CORES DA LOGO ===== */
	--logo-background: #004475;
	--logo-leaf-green: #00a03c;

	/* ===== RADIUS ===== */
	--radius: 0.938rem;
	--radius-controls: 0.625rem;
}

/* ===== TEMA DARK ===== */
.dark {
	/* ===== CORES PRINCIPAIS DARK ===== */
	--primary: #0066cc;
	--primary-foreground: #ffffff;
	--secondary: #1e2a3a;
	--secondary-foreground: #e2e8f0;

	/* ===== CORES DE FUNDO DARK ===== */
	--background: #0f1419;
	--background-secondary: #1a202c;
	--foreground: #ffffff;

	/* ===== CORES DE TEXTO DARK ===== */
	--text-primary: #ffffff;
	--text-secondary: #cbd5e0;

	/* ===== CORES DE BORDA E INPUT DARK ===== */
	--border: #3b475a; /* stronger contrast against card/background */
	--input: #223247; /* slightly lighter input surface for readability */
	--ring: #0066cc;

	/* ===== CORES DE ACENTO DARK ===== */
	--accent: #223246; /* improve contrast for section headers and hovers */
	--accent-foreground: #e2e8f0;
	--muted: #203042; /* subtle surfaces with better separation */
	--muted-foreground: #a0aec0;

	/* ===== CORES DE DESTRUIÇÃO DARK ===== */
	--destructive: #ff5555;
	--destructive-foreground: #ffffff;

	/* ===== CORES ESPECÍFICAS DARK ===== */
	--leaf-green-color: #00d147;

	/* ===== CORES DE CARD DARK ===== */
	--card: #1a202c;
	--card-foreground: #ffffff;

	/* ===== CORES DE POPOVER DARK ===== */
	--popover: #1e2a3a;
	--popover-foreground: #ffffff;
	--ring-background: #0f1419;

	/* ===== CORES DE HEADER DARK ===== */
	--header-bg-from: #0f172a;
	--header-bg-via: #1e293b;
	--header-bg-to: #0f172a;

	/* ===== CORES DE CHART DARK (MAIS VIBRANTES) ===== */
	--chart-1: #4facfe;
	--chart-2: #00f5a0;
	--chart-3: #ffb347;
	--chart-4: #ff6b6b;
	--chart-5: #a78bfa;

	/* ===== CORES DE SIDEBAR DARK ===== */
	--sidebar: #141b26;
	--sidebar-foreground: #ffffff;
	--sidebar-primary: #0066cc;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #1e2a3a;
	--sidebar-accent-foreground: #e2e8f0;
	--sidebar-border: #2d3748;
	--sidebar-ring: #0066cc;

	/* ===== CORES DA LOGO DARK ===== */
	--logo-background: #ffffff;
	--logo-leaf-green: #00d147;
}

/* ===== TEMA LIGHT-GREEN ===== */
.light-green {
	/* ===== CORES PRINCIPAIS LIGHT-GREEN ===== */
	--primary: #00802a;
	--primary-foreground: #ffffff;
	--secondary: #ecfdf5;
	--secondary-foreground: #064e3b;

	/* ===== CORES DE FUNDO LIGHT-GREEN ===== */
	--background: #f8fdf9;
	--background-secondary: #f0fdf4;
	--foreground: #000000;

	/* ===== CORES DE TEXTO LIGHT-GREEN ===== */
	--text-primary: #000000;
	--text-secondary: #374151;

	/* ===== CORES DE BORDA E INPUT LIGHT-GREEN ===== */
	--border: #d1fae5;
	--input: #d1fae5;
	--ring: #00802a;

	/* ===== CORES DE ACENTO LIGHT-GREEN ===== */
	--accent: #ecfdf5;
	--accent-foreground: #064e3b;
	--muted: #ecfdf5;
	--muted-foreground: #6b7280;

	/* ===== CORES DE DESTRUIÇÃO LIGHT-GREEN ===== */
	--destructive: #dc2626;
	--destructive-foreground: #ffffff;

	/* ===== CORES ESPECÍFICAS LIGHT-GREEN ===== */
	--leaf-green-color: #00802a;

	/* ===== CORES DE CARD LIGHT-GREEN ===== */
	--card: #ffffff;
	--card-foreground: #000000;

	/* ===== CORES DE POPOVER LIGHT-GREEN ===== */
	--popover: #ffffff;
	--popover-foreground: #000000;
	--ring-background: #ffffff;

	/* ===== CORES DE HEADER LIGHT-GREEN ===== */
	--header-bg-from: #f0fdf4;
	--header-bg-via: #ffffff;
	--header-bg-to: #f0fdf4;

	/* ===== CORES DE CHART LIGHT-GREEN ===== */
	--chart-1: #00802a;
	--chart-2: #10b981;
	--chart-3: #f59e0b;
	--chart-4: #ef4444;
	--chart-5: #8b5cf6;

	/* ===== CORES DE SIDEBAR LIGHT-GREEN ===== */
	--sidebar: #ffffff;
	--sidebar-foreground: #000000;
	--sidebar-primary: #00802a;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #ecfdf5;
	--sidebar-accent-foreground: #064e3b;
	--sidebar-border: #d1fae5;
	--sidebar-ring: #00802a;

	/* ===== CORES DA LOGO LIGHT-GREEN ===== */
	--logo-background: #004475;
	--logo-leaf-green: #006622;
}

/* ===== TEMA DARK-GREEN ===== */
.dark-green {
	/* ===== CORES PRINCIPAIS DARK-GREEN ===== */
	--primary: #00b83d;
	--primary-foreground: #ffffff;
	--secondary: #1a2e20;
	--secondary-foreground: #d1fae5;

	/* ===== CORES DE FUNDO DARK-GREEN ===== */
	--background: #0a1f0f;
	--background-secondary: #1a2e20;
	--foreground: #ffffff;

	/* ===== CORES DE TEXTO DARK-GREEN ===== */
	--text-primary: #ffffff;
	--text-secondary: #bbf7d0;

	/* ===== CORES DE BORDA E INPUT DARK-GREEN ===== */
	--border: #2d5a35;
	--input: #1a2e20;
	--ring: #00b83d;

	/* ===== CORES DE ACENTO DARK-GREEN ===== */
	--accent: #1a2e20;
	--accent-foreground: #d1fae5;
	--muted: #1a2e20;
	--muted-foreground: #9ca3af;

	/* ===== CORES DE DESTRUIÇÃO DARK-GREEN ===== */
	--destructive: #ff4444;
	--destructive-foreground: #ffffff;

	/* ===== CORES ESPECÍFICAS DARK-GREEN ===== */
	--leaf-green-color: #00e64d;

	/* ===== CORES DE CARD DARK-GREEN ===== */
	--card: #1a2e20;
	--card-foreground: #ffffff;

	/* ===== CORES DE POPOVER DARK-GREEN ===== */
	--popover: #1a2e20;
	--popover-foreground: #ffffff;
	--ring-background: #0a1f0f;

	/* ===== CORES DE HEADER DARK-GREEN ===== */
	--header-bg-from: #0f2818;
	--header-bg-via: #1a2e20;
	--header-bg-to: #0f2818;

	/* ===== CORES DE CHART DARK-GREEN (MAIS VIBRANTES) ===== */
	--chart-1: #00e64d;
	--chart-2: #00f5a0;
	--chart-3: #ffb347;
	--chart-4: #ff6b6b;
	--chart-5: #a78bfa;

	/* ===== CORES DE SIDEBAR DARK-GREEN ===== */
	--sidebar: #142619;
	--sidebar-foreground: #ffffff;
	--sidebar-primary: #00b83d;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #1a2e20;
	--sidebar-accent-foreground: #d1fae5;
	--sidebar-border: #2d5a35;
	--sidebar-ring: #00b83d;

	/* ===== CORES DA LOGO DARK-GREEN ===== */
	--logo-background: #ffffff;
	--logo-leaf-green: #00e64d;
}

@layer base {
	[data-theme="light"] {
		--color-primary: #004475;
		--color-background: #ffffff;
		--color-leaf-green-color: #00a03c;
		--color-sidebar-group-title: #777790;
		--color-logo-background: #004475;
		--color-logo-leaf-green: #00a03c;
	}

	[data-theme="blue-dark"] {
		--color-primary: #004475;
		--color-background: #000000;
		--color-leaf-green-color: #00a03c;
		--color-logo-background: #ffffff;
		--color-logo-leaf-green: #00a03c;
	}

	/* ===== NOVO TEMA DARK ===== */
	[data-theme="dark"] {
		--color-primary: #0066cc;
		--color-background: #0f1419;
		--color-leaf-green-color: #00d147;
		--color-sidebar-group-title: #a0aec0;
		--color-logo-background: #0066cc;
		--color-logo-leaf-green: #00d147;
	}

	/* ===== TEMA LIGHT-GREEN ===== */
	[data-theme="light-green"] {
		--color-primary: #00802a;
		--color-background: #f8fdf9;
		--color-leaf-green-color: #00802a;
		--color-sidebar-group-title: #6b7280;
		--color-logo-background: #00802a;
		--color-logo-leaf-green: #006622;
	}

	/* ===== TEMA DARK-GREEN ===== */
	[data-theme="dark-green"] {
		--color-primary: #00b83d;
		--color-background: #0a1f0f;
		--color-leaf-green-color: #00e64d;
		--color-sidebar-group-title: #9ca3af;
		--color-logo-background: #00b83d;
		--color-logo-leaf-green: #00e64d;
	}
}

@theme inline {
	/* ===== CORES PRINCIPAIS ===== */
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);

	--color-logo-background: var(--logo-background);
	--color-logo-leaf-green: var(--logo-leaf-green);

	/* ===== CORES DE FUNDO ===== */
	--color-background: var(--background);
	--color-background-secondary: var(--background-secondary);
	--color-foreground: var(--foreground);

	/* ===== CORES DE TEXTO ===== */
	--color-text-primary: var(--text-primary);
	--color-text-secondary: var(--text-secondary);

	/* ===== CORES DE BORDA E INPUT ===== */
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);

	/* ===== CORES DE ACENTO ===== */
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);

	/* ===== CORES DE DESTRUIÇÃO ===== */
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);

	/* ===== CORES ESPECÍFICAS ===== */
	--color-leaf-green-color: var(--leaf-green-color);

	/* ===== CORES DE CARD ===== */
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);

	/* ===== CORES DE POPOVER ===== */
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-ring-background: var(--ring-background);

	/* ===== CORES DE HEADER ===== */
	--color-header-bg-from: var(--header-bg-from);
	--color-header-bg-via: var(--header-bg-via);
	--color-header-bg-to: var(--header-bg-to);

	/* ===== CORES DE CHART ===== */
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);

	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);

	/* ===== RADIUS ===== */
	--radius-main: var(--radius);
	--radius-controls: var(--radius-controls);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}

	body {
		@apply bg-background text-foreground;
		/* Transição suave para mudança de tema */
		transition:
			background-color 0.3s ease,
			color 0.3s ease;
	}

	/* ===== ESTILOS ESPECÍFICOS PARA TEMA DARK ===== */
	.dark {
		/* Melhora o contraste para textos */
		color-scheme: dark;
	}

	/* ===== ESTILOS ESPECÍFICOS PARA TEMA LIGHT-GREEN ===== */
	.light-green {
		/* Esquema de cores light com toques verdes */
		color-scheme: light;
	}

	/* ===== ESTILOS ESPECÍFICOS PARA TEMA DARK-GREEN ===== */
	.dark-green {
		/* Esquema de cores dark com toques verdes */
		color-scheme: dark;
	}

	/* Scrollbar personalizada para tema dark */
	.dark ::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	.dark ::-webkit-scrollbar-track {
		background: #1a202c;
	}

	.dark ::-webkit-scrollbar-thumb {
		background: #4a5568;
		border-radius: 4px;
	}

	.dark ::-webkit-scrollbar-thumb:hover {
		background: #0066cc;
	}

	/* Scrollbar personalizada para tema light-green */
	.light-green ::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	.light-green ::-webkit-scrollbar-track {
		background: #f0fdf4;
	}

	.light-green ::-webkit-scrollbar-thumb {
		background: #bbf7d0;
		border-radius: 4px;
	}

	.light-green ::-webkit-scrollbar-thumb:hover {
		background: #00802a;
	}

	/* Scrollbar personalizada para tema dark-green */
	.dark-green ::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	.dark-green ::-webkit-scrollbar-track {
		background: #1a2e20;
	}

	.dark-green ::-webkit-scrollbar-thumb {
		background: #2d5a35;
		border-radius: 4px;
	}

	.dark-green ::-webkit-scrollbar-thumb:hover {
		background: #00b83d;
	}

	.dark button:hover {
		filter: brightness(1.1);
		box-shadow: 0 0 0 1px rgba(0, 102, 204, 0.3);
	}

	.light-green button:hover {
		filter: brightness(1.05);
		box-shadow: 0 0 0 1px rgba(0, 128, 42, 0.3);
	}

	.dark-green button:hover {
		filter: brightness(1.1);
		box-shadow: 0 0 0 1px rgba(0, 184, 61, 0.3);
	}

	.dark input:focus,
	.dark textarea:focus,
	.dark select:focus {
		box-shadow: 0 0 0 2px var(--ring);
		border-color: #0066cc;
	}

	.light-green input:focus,
	.light-green textarea:focus,
	.light-green select:focus {
		box-shadow: 0 0 0 2px var(--ring);
		border-color: #00802a;
	}

	.dark-green input:focus,
	.dark-green textarea:focus,
	.dark-green select:focus {
		box-shadow: 0 0 0 2px var(--ring);
		border-color: #00b83d;
	}

	.dark table {
		background: var(--card);
	}

	.dark-green table {
		background: var(--card);
	}

	.dark th {
		background: var(--accent);
		border-color: var(--border);
	}

	.dark-green th {
		background: var(--accent);
		border-color: var(--border);
	}

	.dark td {
		border-color: var(--border);
	}

	.dark-green td {
		border-color: var(--border);
	}

	.dark tr:hover {
		background: rgba(0, 102, 204, 0.08);
	}

	.light-green tr:hover {
		background: rgba(0, 128, 42, 0.08);
	}

	.dark-green tr:hover {
		background: rgba(0, 184, 61, 0.08);
	}

	/* Efeitos de glow sutis para elementos interativos */
	.dark button:focus,
	.dark [role="button"]:focus {
		box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.4);
	}

	.light-green button:focus,
	.light-green [role="button"]:focus {
		box-shadow: 0 0 0 2px rgba(0, 128, 42, 0.4);
	}

	.dark-green button:focus,
	.dark-green [role="button"]:focus {
		box-shadow: 0 0 0 2px rgba(0, 184, 61, 0.4);
	}

	/* Melhor contraste para links */
	.dark a {
		color: #66b3ff;
	}

	.dark a:hover {
		color: #99ccff;
	}

	.light-green a {
		color: #047857;
	}

	.light-green a:hover {
		color: #00802a;
	}

	.dark-green a {
		color: #34d399;
	}

	.dark-green a:hover {
		color: #6ee7b7;
	}

	/* Elementos de status com toque da cor primária */
	j .dark .badge,
	.dark [class*="badge"] {
		background: rgba(0, 102, 204, 0.15);
		border: 1px solid rgba(0, 102, 204, 0.3);
	}

	.light-green .badge,
	.light-green [class*="badge"] {
		background: rgba(0, 128, 42, 0.15);
		border: 1px solid rgba(0, 128, 42, 0.3);
	}

	.dark-green .badge,
	.dark-green [class*="badge"] {
		background: rgba(0, 184, 61, 0.15);
		border: 1px solid rgba(0, 184, 61, 0.3);
	}
}
