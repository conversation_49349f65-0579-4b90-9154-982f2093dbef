import { IActivityConditionOperator, IActivityLogicOperator } from "../constants/activity";

export interface IActivityFieldDetailsDto {
	id: number;
	field: string;
	sequence: number;
	name: string;
}

export interface IActivityConditionDetailsDto {
	id: number;
	sequence: number;
	field: string;
	operator: IActivityConditionOperator;
	description: string;
	value: string;
	logic: IActivityLogicOperator[] | null;
}

export interface IActivityDetailsDto {
	id: number;
	name: string;
	fields: IActivityFieldDetailsDto[];
	conditions: IActivityConditionDetailsDto[];
}
