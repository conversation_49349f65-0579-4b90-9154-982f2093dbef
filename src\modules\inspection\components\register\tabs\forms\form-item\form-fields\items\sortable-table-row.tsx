import { getFieldValidationErrorsAtom } from "@/modules/inspection/atoms/forms/fields/field-validation.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { useAtomValue } from "jotai";
import { InspectionFieldOptions } from "../options/field-options";

interface SortableTableRowProps {
	row: Row<ICreateFieldForm>;
	isOverlay: boolean;
	mode: "create" | "edit" | "view";
}

export const SortableTableRow = ({ row, isOverlay, mode }: SortableTableRowProps) => {
	const getFieldErrors = useAtomValue(getFieldValidationErrorsAtom);
	const fieldErrors = getFieldErrors(row.original.tempId);
	const hasErrors = Object.keys(fieldErrors).length > 0;

	const { setNodeRef, transform, transition, isDragging } = useSortable({
		id: row.original.tempId,
	});
	const isEmptyGroup = row.original.tempId.includes("empty") || false;
	const hasOptions = row.original.typeId === InspectionFormTypeEnum.OPTIONS && !isOverlay;

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.7 : 1,
	};

	return (
		<>
			<TableRow
				ref={setNodeRef}
				style={style}
				data-field-id={row.original.tempId}
				key={row.id}
				className={`${isEmptyGroup ? "" : "bg-card/80 first:border-t-0 last:border-b-0"} ${hasErrors ? "border-l-destructive bg-destructive/10 border-l-4" : ""} ${mode === "view" ? "view-mode-row" : ""}`}
			>
				{row.getVisibleCells().map(cell => (
					<TableCell key={cell.id} style={{ width: cell.column.columnDef.meta?.width as string }}>
						<div className={`flex w-full flex-col`}>{flexRender(cell.column.columnDef.cell, { ...cell.getContext(), mode })}</div>
					</TableCell>
				))}
			</TableRow>
			{hasOptions && (
				<TableRow>
					<TableCell colSpan={row.getVisibleCells().length} className="p-0">
						<InspectionFieldOptions row={row} mode={mode} />
					</TableCell>
				</TableRow>
			)}
		</>
	);
};
