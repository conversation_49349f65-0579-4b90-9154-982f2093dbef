"use client";

import { SUBJECTS } from "@/config/permissions";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";

export const useDeleteActivityMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: activityQueryKeys.custom("delete"),
			endpoint: (id: string) => ACTIVITY_ENDPOINTS.DELETE(id),
			subject: SUBJECTS.ACTIVITY,
			type: "delete",
			messages: {
				loading: "Excluindo atividade...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: activityQueryKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
