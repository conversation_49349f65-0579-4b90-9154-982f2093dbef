export const runtime = "nodejs";
export const dynamic = "force-dynamic";

import { createRequest } from "@/shared/lib/requests";
import { ICreateRequest } from "@/shared/types/requests/create-request.type";
import { NextRequest, NextResponse } from "next/server";

type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

interface IRequestPayload {
	method: HttpMethod | string;
	path: string;
	body?: unknown;
	options?: Record<string, unknown>;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
	try {
		const payload = (await req.json()) as IRequestPayload;
		const method = (payload.method || "GET").toUpperCase() as HttpMethod;
		const path = payload.path;
		const body = payload.body;
		const options = (payload.options || {}) as Record<string, unknown>;

		if (!path || typeof path !== "string") {
			return NextResponse.json({ success: false, data: { message: "Parâmetro 'path' é obrigatório" }, status: 400 }, { status: 400 });
		}

		const response = await createRequest<{ [key: string]: unknown }>({
			...(options as object),
			path,
			method,
			body,
		} as ICreateRequest<unknown>);

		return NextResponse.json(response, { status: response.status || 200 });
	} catch (error) {
		return NextResponse.json(
			{
				success: false,
				data: { message: "Erro ao processar requisição interna", details: error instanceof Error ? error.message : String(error) },
				status: 500,
			},
			{ status: 500 },
		);
	}
}
