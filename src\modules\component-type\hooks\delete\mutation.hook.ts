"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { COMPONENTS_TYPES_ENDPOINTS } from "../../api/endpoints";
import { componentsKeys } from "../../constants/query";
import { COMPONENT_TYPE_SUBJECTS } from "../../constants/subjects";

export const useDeleteComponentTypeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, number> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, number>(
		{
			mutationKey: componentsKeys.custom("delete"),
			endpoint: (id: number) => COMPONENTS_TYPES_ENDPOINTS.DELETE(id),
			subject: COMPONENT_TYPE_SUBJECTS.COMPONENT_TYPE,
			type: "delete",
			messages: {
				loading: "Deletando tipo de componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: componentsKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
