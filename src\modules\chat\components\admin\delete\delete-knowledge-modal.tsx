import { useDeleteKnowledge } from "@/modules/chat/hooks/delete/mutation.hook";
import { Modal } from "@/shared/components/custom/modal";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Trash } from "lucide-react";

interface DeleteKnowledgeModalProps {
	isOpen: boolean;
	onClose: () => void;
	id: string;
	title: string;
}

export const DeleteKnowledgeModal = ({ isOpen, onClose, id, title }: DeleteKnowledgeModalProps) => {
	const { mutate } = useDeleteKnowledge({ onClose });
	const handleConfirm = () => mutate(id);
	if (!id) return null;

	return (
		<Modal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
			<div className="flex flex-col items-center space-y-4 p-2 text-center">
				<Trash className="h-12 w-12 text-red-500" />

				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Deletar Conhecimento</h2>
					<p className="text-sm text-gray-600">
						Tem certeza que deseja excluir este conhecimento? <span className="text-primary font-medium">{title}</span>
					</p>
				</div>
				<div className="flex gap-3 pt-2">
					<Button variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button className="bg-red-400 hover:bg-red-500" onClick={handleConfirm}>
						Confirmar
					</Button>
				</div>
			</div>
		</Modal>
	);
};
