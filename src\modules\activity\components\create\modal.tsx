import { useAtomValue } from "jotai";
import { activityConditionsAtom } from "../../atoms/conditions.atom";
import { activityFieldsAtom } from "../../atoms/fields.atom";
import { useCreateActivityForm } from "../../hooks/create/form.hook";
import { useCreateActivityMutation } from "../../hooks/create/mutation.hook";
import { useActivityFormReset } from "../../hooks/form/reset.hook";
import { mapToCreateActivityDto } from "../../lib/mappers/create.mapper";
import type { TCreateActivity } from "../../validators/create";
import { ActivityFormModal } from "../form";

interface CreateActivityModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const CreateActivityModal = ({ isOpen, onClose }: CreateActivityModalProps) => {
	const form = useCreateActivityForm();
	const fields = useAtomValue(activityFieldsAtom);
	const conditions = useAtomValue(activityConditionsAtom);
	const { resetAll } = useActivityFormReset();

	const handleClose = () => {
		resetAll(form);
		onClose();
	};

	const { mutate, isLoading } = useCreateActivityMutation({ onClose: handleClose });

	return (
		<ActivityFormModal
			isOpen={isOpen}
			title="Adicionar uma nova atividade"
			description="Crie e configure os campos e regras da nova atividade."
			isSubmitting={isLoading}
			onCancel={handleClose}
			form={form}
			onSubmit={data => {
				const dto = mapToCreateActivityDto(data as unknown as TCreateActivity, { fields, conditions });
				mutate(dto);
			}}
		/>
	);
};
