"use client";

import useCreateMeasures from "@/modules/inspection/hooks/measures/create/form.hook";
import { useCreateMeasureMutation } from "@/modules/inspection/hooks/measures/create/mutation.hook";
import { TCreateMeasures } from "@/modules/inspection/validators/measures/create";
import { Modal } from "@/shared/components/custom/modal";
import FormCreateMeasures from "./form";

interface IModalCreateFormMeasures {
	isOpen: boolean;
	onClose: () => void;
}

export default function ModalCreateMeasures({ isOpen, onClose }: IModalCreateFormMeasures) {
	const { methods } = useCreateMeasures();
	const { mutate } = useCreateMeasureMutation({
		onClose: () => {
			onClose();
			methods.reset();
		},
	});

	function handleSubmit(data: TCreateMeasures) {
		const payload = {
			...data,
			name: data.name,
			abbreviation: data.abbreviation,
		};
		mutate(payload);
	}

	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[500px] !max-w-none" title="Cadastro de Medidas">
			<FormCreateMeasures methods={methods} onSubmit={handleSubmit} onClose={onClose} />
		</Modal>
	);
}
