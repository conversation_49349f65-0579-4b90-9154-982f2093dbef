"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { PRODUCTION_SCRIPT_ENDPOINTS } from "../../api/endpoints";
import { productionScriptQueryKeys } from "../../constants/query";
import { PRODUCTION_SCRIPT_SUBJECTS } from "../../constants/subjects";

export const useDeleteProductScriptMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: productionScriptQueryKeys.custom("delete"),
			endpoint: PRODUCTION_SCRIPT_ENDPOINTS.DELETE,
			subject: PRODUCTION_SCRIPT_SUBJECTS.PRODUCTION_SCRIPT,
			type: "delete",
			messages: {
				loading: "Deletando roteiro de produção...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: productionScriptQueryKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
