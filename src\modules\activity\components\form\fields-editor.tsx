"use client";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { FormControl, FormItem } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { cn } from "@/shared/lib/shadcn/utils";
import { closestCenter, DndContext, DragEndEvent } from "@dnd-kit/core";
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useAtom } from "jotai";
import { GripVertical, Plus, Trash2 } from "lucide-react";
import { useMemo } from "react";
import { activityFieldsAtom } from "../../atoms/fields.atom";
import { ErrorSlot } from "./error-description";

type HandleProps = React.HTMLAttributes<HTMLButtonElement>;

export const FieldsEditor = () => {
	const [fields, setFields] = useAtom(activityFieldsAtom);

	const ids = useMemo(() => fields.map((_, idx) => String(idx)), [fields]);

	const duplicateKeys = useMemo(() => {
		const counts = new Map<string, number>();
		for (const f of fields) {
			const key = (f.field ?? "").trim().toLowerCase();
			if (!key) continue;
			counts.set(key, (counts.get(key) ?? 0) + 1);
		}
		return new Set(
			Array.from(counts.entries())
				.filter(([, c]) => c > 1)
				.map(([k]) => k),
		);
	}, [fields]);

	const requiredErrors = useMemo(() => {
		return fields.map(f => ({
			name: !String(f.name ?? "").trim(),
			field: !String(f.field ?? "").trim(),
		}));
	}, [fields]);

	const addField = () => setFields(prev => [...prev, { name: "", field: "", sequence: prev.length }]);
	const removeField = (index: number) => setFields(prev => prev.filter((_, i) => i !== index).map((f, i) => ({ ...f, sequence: i })));
	const updateField = (index: number, key: "name" | "field", value: string) =>
		setFields(prev => {
			const next = [...prev];
			const curr = next[index];
			if (!curr) return prev;
			if (key === "name") next[index] = { ...curr, name: String(value) };
			else next[index] = { ...curr, field: String(value) };
			return next;
		});

	const onDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;
		if (!over || active.id === over.id) return;
		const oldIndex = Number(active.id);
		const newIndex = Number(over.id);
		const reordered = arrayMove(fields, oldIndex, newIndex).map((f, i) => ({ ...f, sequence: i }));
		setFields(reordered);
	};

	return (
		<div className="flex flex-col gap-4">
			{fields.length === 0 ? (
				<div className="py-8 text-center">
					<p className="text-muted-foreground mb-4 text-sm">Nenhum campo adicionado ainda.</p>
					<Button type="button" onClick={addField} className="mx-auto">
						<Plus className="size-4" /> Adicionar primeiro campo
					</Button>
				</div>
			) : (
				<>
					{/* Tabela de campos */}
					<div className="bg-card rounded-lg border">
						{/* Header da tabela */}
						<div className="bg-muted/50 border-b px-4 py-3">
							<div className="grid grid-cols-[auto_1fr_1fr_auto] items-center gap-4">
								<div className="w-6"></div> {/* Espaço para drag handle */}
								<div className="text-sm font-medium">Nome de Exibição</div>
								<div className="text-sm font-medium">Campo</div>
								<div className="w-10"></div> {/* Espaço para ações */}
							</div>
						</div>

						{/* Corpo da tabela */}
						<DndContext collisionDetection={closestCenter} onDragEnd={onDragEnd}>
							<SortableContext items={ids} strategy={verticalListSortingStrategy}>
								<div>
									{fields.map((item, index) => (
										<SortableRow key={index} id={String(index)} className="border-b last:border-b-0">
											{({ handleProps, setHandleRef }) => (
												<div className="grid grid-cols-[auto_1fr_1fr_auto] items-start gap-4 p-4">
													{/* Drag handle */}
													<button
														type="button"
														aria-label="Arrastar campo"
														className="hover:bg-muted mt-2 flex-shrink-0 cursor-grab rounded p-1 select-none"
														{...handleProps}
														ref={setHandleRef}
													>
														<GripVertical className="text-muted-foreground size-4" />
													</button>

													{/* Nome de exibição */}
													<FormItem className="!gap-1">
														<FormControl>
															<Input
																placeholder="Ex.: Data de início"
																value={item.name}
																autoComplete="off"
																onChange={e => updateField(index, "name", e.target.value)}
																className={cn(
																	"h-9",
																	requiredErrors[index]?.name &&
																		"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																)}
															/>
														</FormControl>
														<ErrorSlot show={!!requiredErrors[index]?.name} message="Nome obrigatório" />
													</FormItem>

													{/* Campo */}
													<FormItem className="!gap-1">
														<FormControl>
															<Input
																placeholder="nome_do_campo"
																value={item.field}
																className={cn(
																	"h-9 font-mono text-sm",
																	duplicateKeys.has((item.field ?? "").trim().toLowerCase()) &&
																		"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																	requiredErrors[index]?.field &&
																		"border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive",
																)}
																onChange={e => updateField(index, "field", e.target.value)}
															/>
														</FormControl>
														{duplicateKeys.has((item.field ?? "").trim().toLowerCase()) ? (
															<ErrorSlot show message="Campo duplicado" />
														) : (
															<ErrorSlot show={!!requiredErrors[index]?.field} message="Campo obrigatório" />
														)}
													</FormItem>

													{/* Ações */}
													<Button
														type="button"
														variant="ghost"
														size="icon"
														onClick={() => removeField(index)}
														className="hover:bg-destructive hover:text-destructive-foreground mt-2 flex-shrink-0"
														aria-label="Remover campo"
													>
														<Trash2 className="size-4" />
													</Button>
												</div>
											)}
										</SortableRow>
									))}
								</div>
							</SortableContext>
						</DndContext>
					</div>

					{/* Botão adicionar */}
					<div className="flex justify-center">
						<Button type="button" variant="outline" onClick={addField}>
							<Plus className="size-4" /> Adicionar campo
						</Button>
					</div>
				</>
			)}
		</div>
	);
};

function SortableRow({
	id,
	className,
	children,
}: {
	id: string;
	className?: string;
	children: (args: { handleProps: HandleProps; setHandleRef: (el: HTMLElement | null) => void }) => React.ReactNode;
}) {
	const { setNodeRef, setActivatorNodeRef, transform, transition, isDragging, attributes, listeners } = useSortable({
		id,
		transition: {
			duration: 200,
			easing: "cubic-bezier(0.25, 1, 0.5, 1)",
		},
	});

	const style: React.CSSProperties = {
		transform: CSS.Transform.toString(transform),
		transition: isDragging ? "none" : transition,
		opacity: isDragging ? 0.6 : 1,
		scale: isDragging ? "1.02" : "1",
		zIndex: isDragging ? 1000 : "auto",
	};

	const handleProps = { ...(attributes as unknown as HandleProps), ...(listeners as unknown as HandleProps) };

	return (
		<div ref={setNodeRef} style={style} className={cn(className, isDragging && "ring-primary/20 ring-2")}>
			{children({ handleProps, setHandleRef: setActivatorNodeRef })}
		</div>
	);
}
