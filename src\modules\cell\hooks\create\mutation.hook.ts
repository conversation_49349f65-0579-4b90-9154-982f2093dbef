"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { CELL_ENDPOINTS } from "../../api/endpoints";
import { cellQueryKeys } from "../../constants/query";
import { CELL_SUBJECTS } from "../../constants/subjects";
import { TCreateCell } from "../../validators/create";

export const useCreateCellMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TCreateCell> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, TCreateCell>(
		{
			mutationKey: cellQueryKeys.custom("create"),
			endpoint: CELL_ENDPOINTS.CREATE,
			subject: CELL_SUBJECTS.CELL,
			type: "create",
			messages: {
				loading: "<PERSON><PERSON><PERSON> célula",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: cellQueryKeys,
		},
		{ ...params },
	);

	return {
		...mutation,
	};
};
