"use client";
import { COLLAB_BY_SECTOR_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { IFindByIdCollabBySectorDTO } from "@/modules/inspection/types/collaborator-by-sector/find-by-id.dto";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryById } from "@/shared/lib/query/process-query-by-id";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookFindByIdReturn } from "@/shared/types/hooks/hook-find-by-id.type";
import { useQuery } from "@tanstack/react-query";

export function useCollabBySectorFindById(formId: string, enabled: boolean): IBaseHookFindByIdReturn<IFindByIdCollabBySectorDTO> {
	const { canRead } = usePermissions();
	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.collabBysector.detail(formId),
		queryFn: () => createGetRequest<IFindByIdCollabBySectorDTO>(COLLAB_BY_SECTOR_ENDPOINTS.FIND_BY_ID(formId)),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR) && enabled,
	});

	return {
		...processQueryById(data, isFetched),
		isLoading,
	};
}
