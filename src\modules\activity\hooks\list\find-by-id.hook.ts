"use client";

import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { processQueryById } from "@/shared/lib/query/process-query-by-id";
import { createGetRequest } from "@/shared/lib/requests";
import { IBaseHookFindByIdReturn } from "@/shared/types/hooks/hook-find-by-id.type";
import { useQuery } from "@tanstack/react-query";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";
import { ACTIVITY_SUBJECTS } from "../../constants/subjects";
import { IActivityDetailsDto } from "../../types/find-by-id.dto";

export const useActivityFindById = (id: string, enabled = true): IBaseHookFindByIdReturn<IActivityDetailsDto> => {
	const { canRead } = usePermissions();

	const isEnabled = canRead(ACTIVITY_SUBJECTS.ACTIVITY) && Boolean(id) && enabled;
	const queryKey = isEnabled ? activityQueryKeys.detail(id.toString()) : activityQueryKeys.custom("detail:pending-id");

	const { data, isLoading, isFetched } = useQuery({
		queryKey,
		queryFn: () => createGetRequest<IActivityDetailsDto>(ACTIVITY_ENDPOINTS.FIND_BY_ID(id.toString())),
		enabled: isEnabled,
	});

	return {
		...processQueryById(data, isFetched),
		isLoading,
	};
};
