import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Link, X } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { GenericSearchSelect } from "../../../../../../../shared/components/custom/generic-search-select";
import { But<PERSON> } from "../../../../../../../shared/components/shadcn/button";
import { useFindAllCell } from "../../../../../../cell/hooks/list/find-all.hook";
import { useFindAllProductTypes } from "../../../../../../product-type/hooks/list/find-all.hook";
import { TCreateCellByProductTypeForm } from "../../../../../validators/cell-by-product-type";
import { requiredLabel } from "../../forms/form-item/form";

interface ICreateCellByProductTypeForm {
	methods: UseFormReturn<TCreateCellByProductTypeForm>;
	onSubmit: (data: TCreateCellByProductTypeForm) => void;
	onClose: () => void;
}

export const FormCreateCellByProductType = ({ methods, onSubmit, onClose }: ICreateCellByProductTypeForm) => {
	return (
		<div className="mx-auto">
			<Form {...methods}>
				<form onSubmit={methods.handleSubmit(onSubmit)}>
					<div className="space-y-5">
						<FormField
							control={methods.control}
							name="cell"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="font-semibold">{requiredLabel("Célula")}</FormLabel>
									<FormControl className="w-full">
										<GenericSearchSelect
											value={field?.value}
											useDataHook={useFindAllCell}
											onChange={value => field.onChange(value)}
											displayField={item => item.name}
											placeholder="Selecione..."
											searchPlaceholder="Buscar célula..."
											loadingText="Carregando..."
											emptyText="Nenhuma célula encontrada."
											width="w-full"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={methods.control}
							name="productType"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="font-semibold">{requiredLabel("Tipo de Produto")}</FormLabel>
									<FormControl className="w-full">
										<GenericSearchSelect
											value={field?.value}
											useDataHook={useFindAllProductTypes}
											displayField={item => item.name}
											onChange={value => field.onChange(value)}
											placeholder="Selecione..."
											searchPlaceholder="Buscar tipo de produto..."
											loadingText="Carregando..."
											emptyText="Nenhum tipo de produto encontrado."
											width="w-full"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					<footer className="mt-8 flex justify-end gap-3 border-t pt-8">
						<Button type="button" variant="outline" onClick={onClose} className="px-6 py-2">
							<X className="mr-2" /> Cancelar
						</Button>
						<Button type="submit" className="bg-primary hover:bg-primary/30 px-6 py-2 text-white">
							<Link className="mr-2" />
							Vincular
						</Button>
					</footer>
				</form>
			</Form>
		</div>
	);
};
