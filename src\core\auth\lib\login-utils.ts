import { pathService } from "@/config/path-manager/service";
import { AUTH_ENDPOINTS } from "@/core/auth/api/endpoints";
import { THEME_PERSISTENCE_CONFIG } from "@/core/theme/constants/preferences";
import { createRawRequest } from "@/shared/lib/requests";
import { cookies } from "next/headers";

interface IErrorContext {
	responseHeaders?: Record<string, string>;
}

type ThemeVariant = "light" | "dark" | "light-green" | "dark-green";
const THEME_VARIANTS: ThemeVariant[] = ["light", "dark", "light-green", "dark-green"];
const isThemeVariant = (value: unknown): value is ThemeVariant => typeof value === "string" && (THEME_VARIANTS as string[]).includes(value);

interface ILoginRequestParams {
	redirectPath: string;
	theme: ThemeVariant;
}

interface IBackendResponse {
	status: number;
	location?: string;
	errorText?: string;
}

async function extractThemeFromCookies(): Promise<ThemeVariant> {
	try {
		const cookieStore = await cookies();
		const themeCookie = cookieStore.get(THEME_PERSISTENCE_CONFIG.cookieName);

		if (themeCookie?.value) {
			const parsedTheme = JSON.parse(themeCookie.value);
			if (isThemeVariant(parsedTheme)) return parsedTheme;
		}
	} catch (error) {
		console.warn("Erro ao extrair tema do cookie:", error);
	}

	return "light";
}

export async function extractLoginParams(request: URL): Promise<ILoginRequestParams> {
	const theme = await extractThemeFromCookies();

	return {
		redirectPath: request.searchParams.get("redirect") || "/",
		theme,
	};
}

export function createErrorResponse(error: string, message: string, status: number): Response {
	return Response.json({ error, message }, { status });
}

export async function fetchBackendLoginUrl(redirectPath: string, theme: ThemeVariant): Promise<IBackendResponse> {
	const params: Record<string, string> = {};
	if (redirectPath !== "/") params.redirect = redirectPath;
	params.theme = theme;

    const response = await createRawRequest<string>({
        path: AUTH_ENDPOINTS.LOGIN,
        method: "GET",
        params,
        maxRedirects: 0,
        validateStatus: status => status < 400,
        withCredentials: true,
        retry: false,
        useCache: false,
    });

	if (response.success) {
		const location = (response.data.headers?.["location"] as string | undefined) ?? undefined;
		return {
			status: response.status,
			location,
			errorText: response.status !== 302 ? (response.data.data as unknown as string) : undefined,
		};
	}

	const errLocation = (response.data?.context as IErrorContext | undefined)?.responseHeaders?.["location"] as string | undefined;
	return {
		status: response.status,
		location: errLocation,
		errorText: response.data?.message || "Erro na comunicação com o servidor de autenticação",
	};
}

export function isRedirectResponse(response: IBackendResponse): boolean {
	return response.status === 302;
}

export function handleRedirectResponse(response: IBackendResponse): Response {
	if (!response.location) throw new Error("URL de redirecionamento não encontrada na resposta do backend");
	return Response.redirect(response.location, 302);
}

export function handleBackendError(response: IBackendResponse): Response {
	const errorMessage = response.errorText || "Erro na comunicação com o servidor de autenticação";
	const status = response.status >= 400 ? response.status : 500;
	return createErrorResponse("backend_error", errorMessage, status);
}

export function isValidRedirectPath(path: string): boolean {
	if (path.includes("://") || path.startsWith("//") || !path.startsWith("/") || ["<", ">", '"', "'", "&"].some(char => path.includes(char))) return false;
	const specialPaths = ["/forbidden"];
	if (specialPaths.includes(path)) return true;
	const item = pathService.getItemByPath(path);
	return item !== undefined;
}
