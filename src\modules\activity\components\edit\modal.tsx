"use client";

import { useAtom } from "jotai";
import { useEffect } from "react";
import { activityConditionsAtom } from "../../atoms/conditions.atom";
import { activityFieldsAtom } from "../../atoms/fields.atom";
import { useCreateActivityForm } from "../../hooks/create/form.hook";
import { useEditActivityMutation } from "../../hooks/edit/mutation.hook";
import { useActivityFormReset } from "../../hooks/form/reset.hook";
import { useActivityFindById } from "../../hooks/list/find-by-id.hook";
import { mapToUpdateActivityDto } from "../../lib/mappers/update.mapper";
import type { TCreateActivity } from "../../validators/create";
import { ActivityFormModal } from "../form";

interface EditActivityModalProps {
	id: string;
	isOpen: boolean;
	onClose: () => void;
}

export const EditActivityModal = ({ id, isOpen, onClose }: EditActivityModalProps) => {
	const form = useCreateActivityForm();
	const [fields, setFields] = useAtom(activityFieldsAtom);
	const [conditions, setConditions] = useAtom(activityConditionsAtom);
	const { data, isLoading: isFetching } = useActivityFindById(id, isOpen);
	const { mutate, isLoading } = useEditActivityMutation({ onClose });
	const { resetAll } = useActivityFormReset();

	useEffect(() => {
		if (!isOpen) return;
		if (!data) return;
		form.reset({ name: data.name });
		setFields(data.fields.map(f => ({ name: f.name, field: f.field, sequence: f.sequence })));
		setConditions(data.conditions);
	}, [isOpen, data, form, setConditions, setFields]);

	const handleClose = () => {
		resetAll(form);
		onClose();
	};

	const handleSubmit = (dataForm: TCreateActivity) => {
		const dto = mapToUpdateActivityDto(dataForm as unknown as TCreateActivity, {
			fields,
			conditions,
			data: data ?? undefined,
		});
		mutate({ id, form: dto });
	};

	return (
		<ActivityFormModal
			isOpen={isOpen}
			title="Editar atividade"
			isSubmitting={isLoading}
			form={form}
			loading={isFetching}
			onCancel={handleClose}
			onSubmit={handleSubmit}
		/>
	);
};
