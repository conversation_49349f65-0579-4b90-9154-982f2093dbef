"use client";

import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { useMutationFactory } from "@/shared/hooks/utils/mutation/mutation-factory.hook";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useDeleteFormMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: inspectionKeys.forms.custom("delete"),
			endpoint: (id: string) => INSPECTION_FORM_ENDPOINTS.DELETE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_FORM,
			type: "delete",
			messages: {
				loading: "Excluindo formulário...",
				success: data => data.message,
				error: error => error.message,
			},
			queryKeys: {
				invalidateAll: queryClient => {
					Promise.all([inspectionKeys.forms.invalidateAll(queryClient), inspectionKeys.formsLink.forceRefresh(queryClient)]);
				},
			},
		},
		{ ...params },
	);

	return {
		...mutation,
	};
};
