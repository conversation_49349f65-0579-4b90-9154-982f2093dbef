"use client";

import { SUBJECTS } from "@/config/permissions";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";
import { IUpdateActivityDto } from "../../types/update.dto";

type TUpdateActivityVariables = { id: string; form: IUpdateActivityDto };

export const useEditActivityMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TUpdateActivityVariables> => {
  const mutation = useMutationFactory<IMessageGlobalReturn, TUpdateActivityVariables>(
    {
      mutationKey: activityQueryKeys.custom("update"),
      endpoint: ({ id }) => ACTIVITY_ENDPOINTS.UPDATE(id),
      subject: SUBJECTS.ACTIVITY,
      type: "put",
      messages: {
        loading: "Atualizando atividade...",
        success: ({ message }) => message,
        error: ({ message }) => message,
      },
      queryKeys: activityQueryKeys,
      transformRequest: ({ form }) => form,
    },
    { ...params },
  );

  return { ...mutation };
};
