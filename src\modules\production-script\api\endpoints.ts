import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IPaginationParameters } from "@/shared/types/pagination/types";

const BASE = "/production-scripts";

// - `POST /production-scripts` - <PERSON><PERSON><PERSON> roteiro
// - `GET /production-scripts` - Listar roteiros (com paginação e filtros)
// - `PUT /production-scripts/{id}` - Atual<PERSON><PERSON> roteiro
// - `DELETE /production-scripts/{id}` - Deletar roteiro

export const PRODUCTION_SCRIPT_ENDPOINTS = Object.freeze({
	CREATE: BASE,
	FIND_ALL: (params: IPaginationParameters) => buildQueryParams(BASE, { ...params }),
	UPDATE: (id: string) => `${BASE}/${encodeURIComponent(id)}`,
	DELETE: (id: string) => `${BASE}/${encodeURIComponent(id)}`,
} as const);
