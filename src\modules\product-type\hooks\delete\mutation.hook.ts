"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { PRODUCT_TYPE_ENDPOINTS } from "../../api/endpoints";
import { productTypeKeys } from "../../constants/query";
import { PRODUCT_TYPE_SUBJECTS } from "../../constants/subjects";

export const useDeleteProductTypeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: productTypeKeys.custom("delete"),
			endpoint: (id: string) => PRODUCT_TYPE_ENDPOINTS.DELETE(id),
			subject: PRODUCT_TYPE_SUBJECTS.PRODUCT_TYPE,
			type: "delete",
			messages: {
				loading: "Excluindo tipo de produto...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: productTypeKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
