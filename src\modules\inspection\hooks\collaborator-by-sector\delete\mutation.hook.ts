"use client";

import { COLLAB_BY_SECTOR_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useDeleteCollabBySectorMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: inspectionKeys.collabBysector.custom("delete"),
			endpoint: (id: string) => COLLAB_BY_SECTOR_ENDPOINTS.DELETE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR,
			type: "delete",
			messages: {
				loading: "Excluindo vinculo de colaborador por setor...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.collabBysector,
		},
		{
			...params,
		},
	);

	return { ...mutation };
};
