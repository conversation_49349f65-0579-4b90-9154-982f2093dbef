import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IPaginationParameters } from "@/shared/types/pagination/types";

const BASE = "/activities";

export const ACTIVITY_ENDPOINTS = Object.freeze({
	CREATE: BASE,
	FIND_ALL: (params?: IPaginationParameters) => buildQueryParams(BASE, { ...params }),
	FIND_BY_ID: (id: string) => `${BASE}/${encodeURIComponent(id)}`,
	UPDATE: (id: string) => `${BASE}/${encodeURIComponent(id)}`,
	DELETE: (id: string) => `${BASE}/${encodeURIComponent(id)}`,
} as const);
