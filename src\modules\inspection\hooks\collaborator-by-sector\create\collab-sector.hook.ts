import { createCollabSectorSchema, TCreateCollabSectorSchema } from "@/modules/inspection/validators/collaborator-by-sector/create";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

type FormValues = TCreateCollabSectorSchema;

export default function useCreateCollabSectorForm() {
	const methods = useForm<FormValues>({
		resolver: zodResolver(createCollabSectorSchema),
		defaultValues: {
			collaborator: undefined,
			sector: undefined,
			pin: "",
			mode: "create" as const,
		},
		mode: "onChange",
	});
	return { methods };
}
