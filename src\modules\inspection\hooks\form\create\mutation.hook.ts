"use client";

import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { ICreateFormDTO } from "@/modules/inspection/types/forms/dtos/create-form.dto";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useCreateFormMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, ICreateFormDTO> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, ICreateFormDTO>(
		{
			mutationKey: inspectionKeys.forms.custom("create"),
			endpoint: INSPECTION_FORM_ENDPOINTS.CREATE,
			subject: INSPECTION_SUBJECTS.INSPECTION_FORM,
			type: "create",
			messages: {
				loading: "Criando formulário...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.forms,
		},
		{ ...params },
	);

	return { ...mutation };
};
