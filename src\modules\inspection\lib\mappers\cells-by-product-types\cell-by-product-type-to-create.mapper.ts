import { ICreateCellByProductTypeDto } from "../../../types/cell-by-product-type/dtos/create.dto";
import { TCreateCellByProductTypeForm } from "../../../validators/cell-by-product-type";

export class CellByProductTypeToCreateMapper {
	static map({ cell, productType }: TCreateCellByProductTypeForm): ICreateCellByProductTypeDto {
		return {
			productionCellId: cell.id,
			productTypeId: productType.id,
		};
	}
}
