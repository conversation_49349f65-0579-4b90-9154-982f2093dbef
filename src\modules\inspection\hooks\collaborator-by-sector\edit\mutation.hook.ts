"use client";
import { COLLAB_BY_SECTOR_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { IUpdateCollabBySectorDTO } from "@/modules/inspection/types/collaborator-by-sector/update.dto";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

type TUpdateCollabBySectorVariables = { id: string; form: IUpdateCollabBySectorDTO };

export function useUpdateCollabBySectorMutation(params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TUpdateCollabBySectorVariables> {
	const mutation = useMutationFactory<IMessageGlobalReturn, TUpdateCollabBySectorVariables>(
		{
			mutationKey: inspectionKeys.collabBysector.custom("update"),
			endpoint: ({ id }) => COLLAB_BY_SECTOR_ENDPOINTS.UPDATE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR,
			type: "patch",
			messages: { loading: "Atualizando setor do colaborador...", success: ({ message }) => message, error: ({ message }) => message },
			queryKeys: inspectionKeys.collabBysector,
			transformRequest: ({ form }) => form,
		},
		{ ...params },
	);
	return { ...mutation };
}
