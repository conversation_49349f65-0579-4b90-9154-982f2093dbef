import { useFindAllFormLinks } from "@/modules/inspection/hooks/form-links/list/find-all.hook";
import { IFormLinkDto } from "@/modules/inspection/types/form-link/dtos/find-all.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";

if (!globalThis.fetch) globalThis.fetch = jest.fn();
jest.mock("@/shared/lib/requests", () => ({
	createGetRequest: jest.fn(),
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canRead: () => true })),
}));

const mockedCreateGetRequest = createGetRequest as jest.MockedFunction<typeof createGetRequest>;

const mockFormFieldsList: IFormLinkDto[] = [
	{
		id: 1,
		form: "Formulário A",
		cell: "Célula 1",
		activity: "Atividade X",
		linkedAt: "2024-06-01T10:00:00Z",
	},
	{
		id: 2,
		form: "Formulário B",
		cell: "Célula 2",
		activity: "Atividade Y",
		linkedAt: "2024-06-02T11:00:00Z",
	},
	{
		id: 3,
		form: "Formulário C",
		cell: "Célula 3",
		activity: "Atividade Z",
		linkedAt: "2024-06-03T12:00:00Z",
	},
];

const mockPaginatedData: IResponsePaginated<IFormLinkDto> = {
	data: mockFormFieldsList,
	totalItems: 3,
	itemCount: 3,
	itemsPerPage: 10,
	currentPage: 1,
	totalPages: 1,
};

const mockSuccessResponse: ApiResponse<IResponsePaginated<IFormLinkDto>> = {
	data: mockPaginatedData,
	success: true,
	status: 200,
};

const mockEmptyResponse: ApiResponse<IResponsePaginated<IFormLinkDto>> = {
	success: false,
	data: { message: "Nenhum vínculo de formulário encontrado" },
	status: 404,
};

const mockErrorResponse: ApiResponse<IResponsePaginated<IFormLinkDto>> = {
	success: false,
	data: { message: "Erro interno do servidor" },
	status: 500,
};

describe("useFindAllFormLinks", () => {
	let queryClient: QueryClient;

	beforeEach(() => {
		queryClient = new QueryClient({
			defaultOptions: {
				queries: {
					retry: false,
				},
			},
		});
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve retornar dados vazios no estado inicial", () => {
		mockedCreateGetRequest.mockImplementation(() => new Promise(() => {}));
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllFormLinks(params), { wrapper });
		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.isLoading).toBe(true);
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(false);
	});

	it("deve retornar dados de vínculos de formulário com sucesso", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllFormLinks(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual(mockFormFieldsList);
		expect(result.current.pagination).toEqual({
			totalItems: 3,
			itemsPerPage: 10,
			currentPage: 1,
			totalPages: 1,
		});
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(false);
		// expect(result.current.error).toBe(false);
	});

	it("deve lidar com resposta vazia (404)", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockEmptyResponse);

		const params = { page: 1, limit: 10, search: "campo inexistente" };
		const { result } = renderHook(() => useFindAllFormLinks(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(true);
		// expect(result.current.error).toBe(false);
	});

	it("deve lidar com erro do servidor", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockErrorResponse);

		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllFormLinks(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.hasError).toBe(true);
		expect(result.current.isEmpty).toBe(false);
		expect(result.current.error).toBe("Erro interno do servidor");
	});

	it("deve funcionar com parâmetros de paginação diferentes", async () => {
		const mockPaginatedResponse: ApiResponse<IResponsePaginated<IFormLinkDto>> = {
			success: true,
			data: {
				...mockPaginatedData,
				currentPage: 2,
				itemsPerPage: 5,
				totalPages: 2,
			},
			status: 200,
		};

		mockedCreateGetRequest.mockResolvedValueOnce(mockPaginatedResponse);

		const params = { page: 2, limit: 5, search: "" };
		const { result } = renderHook(() => useFindAllFormLinks(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.pagination).toEqual({
			totalItems: 3,
			itemsPerPage: 5,
			currentPage: 2,
			totalPages: 2,
		});
	});

	it("deve funcionar com parâmetro de busca", async () => {
		const mockSearchData: IResponsePaginated<IFormLinkDto> = {
			data: [mockFormFieldsList[0]],
			totalItems: 1,
			itemCount: 1,
			itemsPerPage: 10,
			currentPage: 1,
			totalPages: 1,
		};

		const mockSearchResponse: ApiResponse<IResponsePaginated<IFormLinkDto>> = {
			success: true,
			data: mockSearchData,
			status: 200,
		};

		mockedCreateGetRequest.mockResolvedValueOnce(mockSearchResponse);

		const params = { page: 1, limit: 10, search: "teste A" };
		const { result } = renderHook(() => useFindAllFormLinks(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([mockFormFieldsList[0]]);
		expect(result.current.pagination?.totalItems).toBe(1);
	});

	it("deve funcionar sem parâmetros opcionais", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);

		const params = {
			page: 1,
			limit: 10,
			search: "",
		};
		const { result } = renderHook(() => useFindAllFormLinks(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual(mockFormFieldsList);
		expect(mockedCreateGetRequest).toHaveBeenCalledWith(expect.stringContaining("/inspection/forms/links"));
	});

	it("deve lidar com falha na requisição (erro de rede)", async () => {
		mockedCreateGetRequest.mockRejectedValueOnce(new Error("Erro de rede"));

		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllFormLinks(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.hasError).toBe(true);
		expect(result.current.isEmpty).toBe(false);
	});
});
