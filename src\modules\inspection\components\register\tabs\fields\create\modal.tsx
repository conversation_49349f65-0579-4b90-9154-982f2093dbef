import { useCreateFields } from "@/modules/inspection/hooks/fields/create/form.hook";
import { useCreateFieldMutation } from "@/modules/inspection/hooks/fields/create/mutation.hook";
import { ICreateFields } from "@/modules/inspection/validators/fields/create";
import { Modal } from "@/shared/components/custom/modal";
import FormCreateFields from "./form";

interface IModalCreateFormFields {
	isOpen: boolean;
	onClose: () => void;
}

export default function ModalCreateFields({ isOpen, onClose }: IModalCreateFormFields) {
	const { methods } = useCreateFields();
	const { mutate } = useCreateFieldMutation({
		onClose: () => {
			onClose();
			methods.reset();
		},
	});

	function handleSubmit(data: ICreateFields) {
		mutate(data);
	}
	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[500px] !max-w-none" title="Cadastro <PERSON> Campo<PERSON>">
			<FormCreateFields methods={methods} onSubmit={handleSubmit} onClose={onClose} />
		</Modal>
	);
}
