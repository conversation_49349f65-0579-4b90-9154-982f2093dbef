"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { CELL_BY_PRODUCT_TYPE_ENDPOINTS } from "../../../api/endpoints";
import { INSPECTION_SUBJECTS } from "../../../constants/permissions/subjects";
import { inspectionKeys } from "../../../constants/query/keys";

export const useDeleteCellByProductTypeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: inspectionKeys.cellByProductType.custom("delete"),
			endpoint: (id: string) => CELL_BY_PRODUCT_TYPE_ENDPOINTS.DELETE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_PRODUCT_TYPE,
			type: "delete",
			messages: {
				loading: "Deletando célula por tipo de produto...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.cellByProductType,
		},
		{ ...params },
	);

	return { ...mutation };
};
