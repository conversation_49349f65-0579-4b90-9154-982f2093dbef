"use client";

import { SUBJECTS } from "@/config/permissions";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";
import { TCreateActivity } from "../../validators/create";

export const useCreateActivityMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TCreateActivity> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, TCreateActivity>(
		{
			mutationKey: activityQueryKeys.custom("create"),
			endpoint: ACTIVITY_ENDPOINTS.CREATE,
			subject: SUBJECTS.ACTIVITY,
			type: "create",
			messages: {
				loading: "Criando atividade...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: activityQueryKeys,
		},
		{ ...params },
	);

	return {
		...mutation,
	};
};
