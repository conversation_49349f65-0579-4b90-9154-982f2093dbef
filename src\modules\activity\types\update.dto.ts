import { IActivityConditionOperator, IActivityLogicOperator } from "../constants/activity";

export interface IUpdateActivityField {
	id?: number; // opcional para novos campos
	name: string;
	field: string;
	sequence: number;
}

export interface IUpdateActivityCondition {
	id?: number; // opcional para novas condições
	sequence: number;
	description: string;
	field: string;
	operator: IActivityConditionOperator;
	value: string;
	logic: IActivityLogicOperator[] | null;
}

export interface IUpdateActivityDto {
	name: string;
	conditions: IUpdateActivityCondition[];
	fields: IUpdateActivityField[];
}
