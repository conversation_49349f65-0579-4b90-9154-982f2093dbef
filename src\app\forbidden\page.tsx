﻿import { AlertTriangle, Home, ShieldAlert } from "lucide-react";
import Link from "next/link";
import type { ReactElement } from "react";

interface IForbiddenPageProps {
	searchParams: Promise<{
		error?: string;
		message?: string;
	}>;
}

const AUTH_ERRORS = [
	"keycloak_auth_error",
	"missing_parameters",
	"missing_state",
	"invalid_state",
	"token_exchange_error",
	"token_storage_error",
	"internal_error",
];

const getErrorInfo = (error?: string, message?: string) => {
	const isAuthError = error && AUTH_ERRORS.includes(error);
	return {
		isAuthError,
		title: isAuthError ? "Erro de Autenticação" : "Não Autorizado",
		description:
			message ||
			(isAuthError
				? "Ocorreu um erro durante o processo de autenticação. Tente fazer login novamente."
				: "Você não possui permissão para acessar esta página"),
		icon: isAuthError ? (
			<AlertTriangle className="text-destructive h-14 w-14 drop-shadow-lg" />
		) : (
			<ShieldAlert className="text-primary h-14 w-14 drop-shadow-lg" />
		),
		iconBg: isAuthError ? "bg-gradient-to-br from-destructive/20 to-destructive/10" : "bg-gradient-to-br from-primary/20 to-secondary/10",
		titleBg: isAuthError ? "bg-destructive" : "bg-primary",
		descText: isAuthError ? "text-destructive" : "text-primary",
		btnBg: isAuthError ? "bg-destructive" : "bg-primary",
		btnRing: isAuthError ? "focus:ring-destructive/40" : "focus:ring-primary/40",
	};
};

export default async function ForbiddenPage({ searchParams }: IForbiddenPageProps): Promise<ReactElement> {
	const { error, message } = await searchParams;
	const { isAuthError, title, description, icon, iconBg, titleBg, descText } = getErrorInfo(error, message);

	return (
		<div
			className="from-primary/15 to-background flex min-h-screen flex-col items-center justify-center bg-gradient-to-br px-4"
			role="alert"
			aria-live="assertive"
		>
			<div className="ring-primary/20 w-full max-w-2xl rounded-2xl border border-gray-100 bg-white/90 p-10 shadow-2xl ring-2 backdrop-blur-xl">
				<div className="flex flex-col items-center">
					<span className={`inline-flex items-center justify-center rounded-full ${iconBg} animate-fade-in-forbidden mb-6 p-6 shadow-xl`}>
						{icon}
					</span>
					<h1
						className={`bg-clip-text text-5xl font-extrabold text-transparent ${titleBg} animate-fade-in-forbidden mb-2 text-center tracking-tight drop-shadow-xl`}
					>
						{title}
					</h1>
					<h2 className={`text-2xl font-semibold ${descText} animate-fade-in-forbidden mb-6 text-center drop-shadow delay-100`}>{description}</h2>
					<div className="animate-fade-in-forbidden mb-6 flex flex-wrap justify-center gap-4 delay-200">
						<Link
							href="/"
							className={`bg-primary focus:ring-primary/40 inline-flex items-center gap-2 rounded-xl px-7 py-3 text-base font-bold text-white shadow-lg transition-transform duration-200 hover:scale-105 hover:shadow-2xl focus:ring-2 focus:outline-none`}
						>
							<Home className="h-5 w-5" />
							Página Inicial
						</Link>
						{isAuthError && (
							<Link
								href="/routes/login"
								className="bg-destructive focus:ring-destructive/40 inline-flex items-center gap-2 rounded-xl px-7 py-3 text-base font-bold text-white shadow-lg transition-transform duration-200 hover:scale-105 hover:shadow-2xl focus:ring-2 focus:outline-none"
							>
								<ShieldAlert className="h-5 w-5" />
								Tentar Novamente
							</Link>
						)}
					</div>
					<p className="animate-fade-in-forbidden max-w-md text-center text-sm text-gray-500 delay-300">
						Se você acredita que isso é um erro, entre em contato com <span className="text-primary font-semibold">administrador do sistema.</span>
					</p>
				</div>
			</div>
		</div>
	);
}
