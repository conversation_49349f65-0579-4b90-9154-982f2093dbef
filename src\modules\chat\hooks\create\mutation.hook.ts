"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";
import { TAddKnowledgeSchema } from "../../validators/add-knowledge";

export const useAddKnowledgeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TAddKnowledgeSchema> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, TAddKnowledgeSchema>(
		{
			mutationKey: chatKeys.custom("add-knowledge"),
			endpoint: CHAT_ENDPOINTS.ADD_KNOWLEDGE,
			subject: "all",
			type: "create",
			messages: {
				loading: "Adicionando conhecimento...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: chatKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
