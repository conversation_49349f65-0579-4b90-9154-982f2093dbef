"use client";

import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { ICreateFieldsDTO } from "@/modules/inspection/types/fields/create-fields.dto";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { inspectionKeys } from "../../../constants/query/keys";

export const useCreateFieldMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, ICreateFieldsDTO> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, ICreateFieldsDTO>(
		{
			mutationKey: inspectionKeys.fields.custom("create"),
			endpoint: FIELDS_ENDPOINTS.CREATE,
			subject: INSPECTION_SUBJECTS.INSPECTION_FIELDS,
			type: "create",
			messages: {
				loading: "Criando campo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.fields,
		},
		{ ...params },
	);

	return { ...mutation };
};
