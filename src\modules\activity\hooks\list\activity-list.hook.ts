"use client";

import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { usePagination } from "@/shared/hooks/utils";
import { useFindAllActivity } from "./find-all.hook";

interface UseActivityProps {
  searchTerm: string;
}

export const useTableActivity = ({ searchTerm }: UseActivityProps) => {
  const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();
  const isMobile = useIsMobile();

  const { data, isLoading, error, pagination, hasError, isEmpty } = useFindAllActivity({
    limit: pageSize,
    page: currentPage,
    search: searchTerm || "",
  });

  const handlePageSizeChange = (size: number) => {
    setItemsPerPage(size);
    setCurrentPage(1);
  };

  return {
    data,
    isLoading,
    isEmpty,
    error,
    hasError,
    pagination,
    pageSize,
    isMobile,
    handlePageSizeChange,
    setCurrentPage,
  };
};

