"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { PRODUCT_TYPE_ENDPOINTS } from "../../api/endpoints";
import { productTypeKeys } from "../../constants/query";
import { PRODUCT_TYPE_SUBJECTS } from "../../constants/subjects";
import { TCreateProductType } from "../../validators/create";

export const useCreateProductTypeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TCreateProductType> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, TCreateProductType>(
		{
			mutationKey: productTypeKeys.custom("create"),
			endpoint: PRODUCT_TYPE_ENDPOINTS.CREATE,
			subject: PRODUCT_TYPE_SUBJECTS.PRODUCT_TYPE,
			type: "create",
			messages: {
				loading: "Criando tipo de produto...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: productTypeKeys,
		},
		{ ...params },
	);

	return {
		...mutation,
	};
};
