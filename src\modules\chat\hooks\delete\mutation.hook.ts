"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";

export const useDeleteKnowledge = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: chatKeys.custom("delete-knowledge"),
			endpoint: (id: string) => CHAT_ENDPOINTS.REMOVE_KNOWLEDGE(id),
			subject: "all",
			type: "delete",
			messages: {
				loading: "Deletando conhecimento...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: chatKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
