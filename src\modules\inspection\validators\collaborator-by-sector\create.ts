import z from "zod";

export const createCollabSectorSchema = z
	.object({
		collaborator: z.object({
			id: z.string(),
			name: z.string(),
		}),
		sector: z.object({ id: z.number(), name: z.string() }),
		pin: z.string().optional(),
		mode: z.enum(["create", "edit"]).optional(),
	})
	.refine(
		data => {
			if (data.mode === "create" && !data.pin) {
				return false;
			}
			return true;
		},
		{
			message: "O PIN é obrigatório na criação.",
			path: ["pin"],
		},
	)
	.refine(
		data => {
			if (data.pin && (data.pin.length < 4 || data.pin.length > 4)) {
				return false;
			}
			return true;
		},
		{
			message: "O PIN deve ter exatamente 4 dígitos.",
			path: ["pin"],
		},
	);

export type TCreateCollabSectorSchema = z.infer<typeof createCollabSectorSchema>;
