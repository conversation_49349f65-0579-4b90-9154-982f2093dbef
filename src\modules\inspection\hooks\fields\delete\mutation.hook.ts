"use client";

import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { inspectionKeys } from "../../../constants/query/keys";

export const useDeleteFieldMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: inspectionKeys.fields.custom("delete"),
			endpoint: (id: string) => FIELDS_ENDPOINTS.DELETE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_FIELDS,
			type: "delete",
			messages: {
				loading: "Excluindo campo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.fields,
		},
		{ ...params },
	);

	return { ...mutation };
};
