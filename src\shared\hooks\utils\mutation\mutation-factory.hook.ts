"use client";

import { TPermissionSubject } from "@/config/permissions";
import { toast } from "@/core/toast";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createDeleteRequest, createPatchRequest, createPostRequest, createPutRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCallback } from "react";
import { IMutationOptions, IMutationReturn, MutationConfig } from "./types";

const PERMISSION_METHODS = {
	create: "canCreate",
	patch: "canUpdate",
	delete: "canDelete",
	put: "canUpdate",
} as const;

const DEFAULT_ERROR_MESSAGES = {
	create: "Sem permissão para criar",
	patch: "Sem permissão para atualizar",
	put: "Sem permissão para atualizar",
	delete: "Sem permissão para excluir",
} as const;

export function useMutationFactory<TData = IMessageGlobalReturn, TVariables = unknown>(
	config: MutationConfig<TData, TVariables>,
	options: IMutationOptions = {},
): IMutationReturn<TData, TVariables> {
	const queryClient = useQueryClient();
	const permissions = usePermissions();
	const permissionMethod = PERMISSION_METHODS[config.type];

	const { onClose, disableToast = false, disableInvalidation = false } = options;

	const mutation = useMutation({
		mutationKey: config.mutationKey,
		mutationFn: async (variables: TVariables) => {
			if (config.customPermissionCheck) {
				const customCheck = config.customPermissionCheck(variables);
				if (typeof customCheck === "string") throw new Error(customCheck);
				if (!customCheck) throw new Error(DEFAULT_ERROR_MESSAGES[config.type]);
			} else {
				const hasPermission = (permissions[permissionMethod] as (subject: TPermissionSubject) => boolean)(config.subject);
				if (!hasPermission) throw new Error(DEFAULT_ERROR_MESSAGES[config.type]);
			}

			const requestData = config.transformRequest ? config.transformRequest(variables) : variables;
			const endpoint = typeof config.endpoint === "function" ? config.endpoint(variables) : config.endpoint;
			let response;

			if (config.type === "delete") {
				response = await createDeleteRequest<TData>(endpoint);
			} else if (config.type === "create") {
				response = await createPostRequest<TData>(endpoint, requestData);
			} else if (config.type === "patch") {
				response = await createPatchRequest<TData>(endpoint, requestData);
			} else {
				response = await createPutRequest<TData>(endpoint, requestData);
			}

			if (!response.success) throw new Error(response.data.message || "Erro na requisição");
			return config.mapResponseToData ? config.mapResponseToData(response.data) : response.data;
		},
		onSuccess: (data, variables) => {
			if (!disableInvalidation) config.queryKeys.invalidateAll(queryClient);
			config.onSuccess?.(data, variables);
			onClose?.();
		},
		onError: (error, variables) => {
			config.onError?.(error as Error, variables);
		},
	});

	const mutate = useCallback(
		async (variables: TVariables): Promise<TData> => {
			if (disableToast) {
				return mutation.mutateAsync(variables);
			}

			return toast.promise(mutation.mutateAsync(variables), {
				loading: config.messages.loading,
				success: data => {
					if (typeof config.messages.success === "function") {
						return config.messages.success(data);
					}
					return config.messages.success || "Operação realizada com sucesso!";
				},
				error: error => {
					if (typeof config.messages.error === "function") {
						return config.messages.error(error);
					}
					return config.messages.error || error.message || "Erro na operação";
				},
			});
		},
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[mutation.mutateAsync, disableToast],
	);

	return {
		mutate,
		data: mutation.data,
		isLoading: mutation.isPending,
		error: mutation.error,
		isSuccess: mutation.isSuccess,
		reset: mutation.reset,
		mutateAsync: mutation.mutateAsync,
	};
}
