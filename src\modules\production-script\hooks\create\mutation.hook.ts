"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { PRODUCTION_SCRIPT_ENDPOINTS } from "../../api/endpoints";
import { productionScriptQueryKeys } from "../../constants/query";
import { PRODUCTION_SCRIPT_SUBJECTS } from "../../constants/subjects";
import { IProductionScriptCreateDto } from "../../types/dtos/create.dto";

export const useCreateProductScriptMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, IProductionScriptCreateDto> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, IProductionScriptCreateDto>(
		{
			mutationKey: productionScriptQueryKeys.custom("create"),
			endpoint: PRODUCTION_SCRIPT_ENDPOINTS.CREATE,
			subject: PRODUCTION_SCRIPT_SUBJECTS.PRODUCTION_SCRIPT,
			type: "create",
			messages: {
				loading: "Criando roteiro de produção...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: productionScriptQueryKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
