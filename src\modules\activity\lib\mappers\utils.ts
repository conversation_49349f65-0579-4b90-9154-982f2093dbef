import type { IAcitivityCondition, IActivityField } from "../../types/create.dto";

export const sortBySequence = <T extends { sequence: number }>(items: T[]): T[] => {
  return [...items].sort((a, b) => a.sequence - b.sequence);
};

export const uniqueBy = <T, K>(items: T[], keyFn: (item: T) => K): T[] => {
  const seen = new Set<K>();
  return items.filter(item => {
    const key = keyFn(item);
    if (seen.has(key)) return false;
    seen.add(key);
    return true;
  });
};

export const ensureSequence = (zeroBased?: number, idxFallback?: number): number => {
  const candidate = Number((zeroBased ?? 0) + 1);
  if (Number.isFinite(candidate) && candidate > 0) return candidate;
  if (typeof idxFallback === "number" && Number.isFinite(idxFallback)) return idxFallback + 1;
  return 1;
};

export const normalizeFieldInput = (f: Pick<IActivityField, "name" | "field" | "sequence">, idxFallback?: number) => {
  return {
    name: String(f.name ?? "").trim(),
    field: String(f.field ?? "").trim(),
    sequence: ensureSequence(f.sequence, idxFallback),
  } satisfies IActivityField;
};

export const normalizeConditionInput = (
  c: Pick<IAcitivityCondition, "sequence" | "description" | "field" | "operator" | "value" | "logic">,
  idxFallback?: number,
) => {
  const logic = Array.isArray(c.logic) && c.logic.length > 0 ? c.logic : null;
  return {
    sequence: ensureSequence(c.sequence, idxFallback),
    description: String(c.description ?? "").trim(),
    field: String(c.field ?? "").trim(),
    operator: c.operator,
    value: String(c.value ?? "").trim(),
    logic,
  } satisfies IAcitivityCondition;
};

export const conditionKeyFromValues = (field: string, sequence: number, description: string) => {
  return `${String(field ?? "").trim()}|${sequence}|${String(description ?? "").trim()}` as const;
};

export const conditionKeyFromInput = (
  c: Pick<IAcitivityCondition, "sequence" | "description" | "field">,
  idxFallback?: number,
) => {
  const seq = ensureSequence(c.sequence, idxFallback);
  return conditionKeyFromValues(c.field, seq, c.description);
};

