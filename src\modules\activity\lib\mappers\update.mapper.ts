import { IAcitivityCondition, IActivityField } from "../../types/create.dto";
import type { IActivityDetailsDto } from "../../types/find-by-id.dto";
import type { IUpdateActivityDto } from "../../types/update.dto";
import type { TCreateActivity } from "../../validators/create";
import { conditionKeyFromInput, normalizeConditionInput, normalizeFieldInput, sortBySequence, uniqueBy } from "./utils";

type UpdateMapperOptions = {
	fields?: IActivityField[];
	conditions?: IAcitivityCondition[];
	data?: IActivityDetailsDto;
};

export const mapToUpdateActivityDto = (form: TCreateActivity, opts?: UpdateMapperOptions): IUpdateActivityDto => {
	const name = form.name.trim();

	const fieldsInput = opts?.fields ?? [];
	const conditionsInput = opts?.conditions ?? [];

	const originalFields = opts?.data?.fields ?? [];
	const originalConditions = opts?.data?.conditions ?? [];

	const originalFieldsByField = new Map(originalFields.map(f => [f.field, { id: f.id, field: f.field }]));
	const originalConditionsByKey = new Map(
		originalConditions.map(c => [
			`${c.field}|${c.sequence}|${c.description}` as const,
			{ id: c.id, sequence: c.sequence, description: c.description, field: c.field, value: c.value, logic: c.logic },
		]),
	);

	const fields = sortBySequence(
		uniqueBy(
			(fieldsInput ?? []).map((f, idx) => ({
				id: originalFieldsByField.get(f.field)?.id,
				...normalizeFieldInput(f, idx),
			})),
			f => f.field,
		),
	);

	const conditions = sortBySequence(
		(conditionsInput ?? [])
			.map((c, idx) => {
				const key = conditionKeyFromInput(c, idx);
				const original = originalConditionsByKey.get(key);
				return {
					id: original?.id,
					...normalizeConditionInput(c, idx),
				};
			})
			.filter(c => c.description && c.field),
	);

	return { name, fields, conditions } satisfies IUpdateActivityDto;
};
