import { pathService } from "@/config/path-manager/service";
import { IUser } from "@/core/auth/types/user.types";
import { defineAbilitiesFor } from "@/shared/lib/permissions/ability";
import { NextRequest, NextResponse } from "next/server";
import { TokenValidationService } from "./token-validation.service";

export interface IUnifiedAuthResult {
	isAuthenticated: boolean;
	hasPermission: boolean;
	redirectUrl?: string;
	statusCode: number;
}

export class UnifiedAuthMiddleware {
	private static tokenService = TokenValidationService.getInstance();

	public static async process(request: NextRequest): Promise<NextResponse> {
		try {
			const { pathname } = request.nextUrl;
			if (this.isPublicRoute(pathname)) return NextResponse.next();
			const validationResult = await this.tokenService.validateToken(request);
			if (!validationResult.isValid) return this.createRedirectResponse("/routes/login", pathname, request.url);
			if (!validationResult.user) return this.createRedirectResponse("/routes/login", pathname, request.url);
			const permissionResult = await this.checkRoutePermissions(pathname, validationResult.user);
			if (!permissionResult.hasPermission) return this.createRedirectResponse("/forbidden", pathname, request.url);
			return NextResponse.next();
		} catch {
			return this.createRedirectResponse("/routes/login", request.nextUrl.pathname, request.url);
		}
	}

	private static isPublicRoute(pathname: string): boolean {
		return (
			pathname.startsWith("/routes/") ||
			pathname.startsWith("/api/") ||
			pathname.includes(".") ||
			pathname === "/favicon.ico" ||
			pathname === "/404" ||
			pathname === "/forbidden"
		);
	}

	private static async checkRoutePermissions(pathname: string, user: IUser): Promise<{ hasPermission: boolean }> {
		try {
			const currentRoute = pathService.getItemByPath(pathname);
			if (currentRoute && !currentRoute.route.active) return { hasPermission: false };
			const ability = defineAbilitiesFor(user);
			const hasPermission = currentRoute ? pathService.hasPermission(currentRoute, ability) : false;
			return { hasPermission };
		} catch (error) {
			console.error("Erro ao verificar permissões:", error);
			return { hasPermission: false };
		}
	}

	private static createRedirectResponse(targetPath: string, originalPath: string, basePath: string): NextResponse {
		const redirectTo = new URL(targetPath, basePath);
		if (targetPath === "/routes/login") redirectTo.searchParams.set("redirect", originalPath);
		return NextResponse.redirect(redirectTo, { status: 302 });
	}

	public static clearCache(): void {
		this.tokenService.clearCache();
	}

	public static getCacheSize(): number {
		return this.tokenService.getCacheSize();
	}
}
