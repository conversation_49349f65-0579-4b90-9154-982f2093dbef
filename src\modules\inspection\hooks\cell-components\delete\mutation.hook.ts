"use client";

import { SUBJECTS } from "@/config/permissions";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { CELL_COMPONENTS_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";

export const useDeleteCellByComponentMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: inspectionKeys.cellByComponents.custom("delete"),
			endpoint: (id: string) => CELL_COMPONENTS_ENDPOINTS.DELETE(id),
			subject: SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_COMPONENT,
			type: "delete",
			messages: {
				loading: "Deletando componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.cellByComponents,
		},
		{ ...params },
	);

	return { ...mutation };
};
