"use client";

import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import { useFindAllActivity } from "../../hooks/list/find-all.hook";
import { IActivityDto } from "../../types/find-all.dto";

if (!globalThis.fetch) globalThis.fetch = jest.fn();
jest.mock("../../../../shared/lib/requests", () => ({
	createGetRequest: jest.fn(),
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canRead: () => true })),
}));

const mockedCreateGetRequest = createGetRequest as jest.MockedFunction<typeof createGetRequest>;

const mockActivityList: IActivityDto[] = [
	{ id: 1, name: "Atividade 1" },
	{ id: 2, name: "Atividade 2" },
	{ id: 3, name: "Atividade 3" },
];

const mockPaginatedData: IResponsePaginated<IActivityDto> = {
	data: mockActivityList,
	totalItems: 3,
	itemCount: 3,
	itemsPerPage: 10,
	currentPage: 1,
	totalPages: 1,
};

const mockSuccessResponse: ApiResponse<IResponsePaginated<IActivityDto>> = {
	data: mockPaginatedData,
	success: true,
	status: 200,
};

const mockEmptyResponse: ApiResponse<IResponsePaginated<IActivityDto>> = {
	success: false,
	data: { message: "Nenhuma atividade encontrada" },
	status: 404,
};

const mockErrorResponse: ApiResponse<IResponsePaginated<IActivityDto>> = {
	success: false,
	data: { message: "Erro interno do servidor" },
	status: 500,
};

describe("useFindAllActivity", () => {
	let queryClient: QueryClient;

	beforeEach(() => {
		queryClient = new QueryClient({
			defaultOptions: {
				queries: {
					retry: false,
				},
			},
		});
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve retornar dados vazios no estado inicial", () => {
		mockedCreateGetRequest.mockImplementation(() => new Promise(() => {}));
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllActivity(params), { wrapper });
		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.isLoading).toBe(true);
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBeFalsy();
	});

	it("deve retornar dados de setores com sucesso", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllActivity(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual(mockActivityList);
		expect(result.current.pagination).toEqual({
			totalItems: 3,
			itemsPerPage: 10,
			currentPage: 1,
			totalPages: 1,
		});
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(false);
		expect(result.current.error).toBeUndefined();
	});

	it("deve lidar com resposta vazia (404)", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockEmptyResponse);

		const params = { page: 1, limit: 10, search: "setor inexistente" };
		const { result } = renderHook(() => useFindAllActivity(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(true);
		expect(result.current.error).toBeUndefined();
	});

	it("deve lidar com erro do servidor", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockErrorResponse);

		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllActivity(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.hasError).toBe(true);
		expect(result.current.isEmpty).toBe(false);
		expect(result.current.error).toBe("Erro interno do servidor");
	});

	it("deve funcionar com parâmetros de paginação diferentes", async () => {
		const mockPaginatedResponse: ApiResponse<IResponsePaginated<IActivityDto>> = {
			success: true,
			data: {
				...mockPaginatedData,
				currentPage: 2,
				itemsPerPage: 5,
				totalPages: 2,
			},
			status: 200,
		};

		mockedCreateGetRequest.mockResolvedValueOnce(mockPaginatedResponse);

		const params = { page: 2, limit: 5, search: "" };
		const { result } = renderHook(() => useFindAllActivity(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.pagination).toEqual({
			totalItems: 3,
			itemsPerPage: 5,
			currentPage: 2,
			totalPages: 2,
		});
	});

	it("deve funcionar com parâmetro de busca", async () => {
		const mockSearchData: IResponsePaginated<IActivityDto> = {
			data: [mockActivityList[0]],
			totalItems: 1,
			itemCount: 1,
			itemsPerPage: 10,
			currentPage: 1,
			totalPages: 1,
		};

		const mockSearchResponse: ApiResponse<IResponsePaginated<IActivityDto>> = {
			success: true,
			data: mockSearchData,
			status: 200,
		};

		mockedCreateGetRequest.mockResolvedValueOnce(mockSearchResponse);

		const params = { page: 1, limit: 10, search: "Setor A" };
		const { result } = renderHook(() => useFindAllActivity(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([mockActivityList[0]]);
		expect(result.current.pagination?.totalItems).toBe(1);
	});

	it("deve funcionar sem parâmetros opcionais", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);

		const params = {};
		const { result } = renderHook(() => useFindAllActivity(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual(mockActivityList);
		expect(mockedCreateGetRequest).toHaveBeenCalledWith(expect.stringContaining("/activities"));
	});

	it("deve lidar com falha na requisição (erro de rede)", async () => {
		mockedCreateGetRequest.mockRejectedValueOnce(new Error("Erro de rede"));

		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllActivity(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.hasError).toBe(true);
		expect(result.current.isEmpty).toBeFalsy();
	});
});
