"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { COMPONENTS_TYPES_ENDPOINTS } from "../../api/endpoints";
import { componentsKeys } from "../../constants/query";
import { COMPONENT_TYPE_SUBJECTS } from "../../constants/subjects";
import { TCreateComponentType } from "../../validators/create.validator";

export const useCreateComponentTypeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TCreateComponentType> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, TCreateComponentType>(
		{
			mutationKey: componentsKeys.custom("create"),
			endpoint: COMPONENTS_TYPES_ENDPOINTS.CREATE,
			subject: COMPONENT_TYPE_SUBJECTS.COMPONENT_TYPE,
			type: "create",
			messages: {
				loading: "Criando tipo de componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: componentsKeys,
		},
		{ ...params },
	);

	return { ...mutation };
};
