import { cn } from "@/shared/lib/shadcn/utils";
import { LucideIcon } from "lucide-react";
import { ReactNode } from "react";

interface SectionProps {
	title: string;
	description?: string;
	action?: ReactNode;
	children: ReactNode;
	className?: string;
	icon?: LucideIcon;
}

export const Section = ({ title, action, children, className, icon: Icon }: SectionProps) => {
    return (
		<div className={cn("bg-card rounded-lg border", className)}>
			<div className="bg-accent flex items-center justify-between border-b px-4 py-3">
				<div className="flex items-center gap-2">
					{Icon && <Icon className="text-muted-foreground size-4" />}
					<h3 className="text-sm font-medium">{title}</h3>
				</div>
				{action && <div>{action}</div>}
			</div>
			<div className="space-y-3 p-4">{children}</div>
		</div>
    );
};
