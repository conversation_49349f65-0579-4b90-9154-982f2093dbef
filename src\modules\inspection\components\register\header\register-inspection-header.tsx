"use client";
import { TPermissionSubject } from "@/config/permissions";
import { TInspectionTabValue } from "@/modules/inspection/hooks/tabs/inspection-tabs.hook";
import { ProtectedComponent } from "@/shared/components/auth/protected";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, Sheet<PERSON><PERSON>le, SheetTrigger } from "@/shared/components/shadcn/sheet";
import { TabsList, TabsTrigger } from "@/shared/components/shadcn/tabs";
import { useIsMobile } from "@/shared/hooks/utils/media-query.hook";
import { LucideIcon, Menu } from "lucide-react";
import React, { useState } from "react";

interface IRegisterInspectionHeaderProps {
	tabItems: ITabItemRegisterInspectionTabs[];
	activeTab: TInspectionTabValue;
	setActiveTab: (tab: TInspectionTabValue) => void;
	searchTerm: string;
	setSearchTerm: (term: string) => void;
	onNew: (searchTerm: string) => void;
}

export interface ITabItemRegisterInspectionTabs {
	requiredPermission: TPermissionSubject;
	value: TInspectionTabValue;
	label: string;
	icon: LucideIcon;
	renderContent: (searchTerm: string) => React.ReactNode;
	onNew: (searchTerm: string) => void;
	disabled?: boolean;
}

export const RegisterInspectionHeader: React.FC<IRegisterInspectionHeaderProps> = ({ tabItems, activeTab, setActiveTab }) => {
	const isMobile = useIsMobile();
	const [isSheetOpen, setIsSheetOpen] = useState(false);

	const activeTabItem = tabItems.find(tab => tab.value === activeTab);

	const shouldUseMobileLayout = isMobile;

	const handleTabSelect = (value: TInspectionTabValue) => {
		setActiveTab(value);
		setIsSheetOpen(false);
	};

	const TabButton: React.FC<{ item: ITabItemRegisterInspectionTabs; variant?: "default" | "mobile" }> = ({ item, variant = "default" }) => {
		const { value, label, icon: Icon, disabled } = item;
		const isActive = activeTab === value;

		const baseClasses = `
			flex items-center gap-3 px-4 py-3 transition-all duration-200 ease-in-out
			${disabled ? "cursor-not-allowed border border-dashed border-border bg-muted text-muted-foreground opacity-60" : "cursor-pointer"}
		`;

		if (variant === "mobile") {
			return (
				<button
					onClick={() => !disabled && handleTabSelect(value)}
					disabled={disabled}
					className={` ${baseClasses} rounded-controls w-full justify-start border text-left ${isActive ? "bg-primary border-primary text-text-primary scale-[1.03] shadow-lg" : disabled ? "border-border" : "hover:bg-primary/10 hover:text-primary bg-background border-border hover:border-primary/30 hover:shadow-sm"} ${isActive ? "ring-primary/40 ring-2" : ""} `}
					title={disabled ? "Funcionalidade em breve" : label}
					style={{
						transition: "box-shadow 0.2s, transform 0.2s",
					}}
				>
					<div className={`flex items-center gap-3 ${isActive ? "" : "text-primary/80"}`}>
						<Icon className={`size-5 ${isActive ? "text-white" : "text-text-primary"}`} />
						<span className={`text-base font-semibold ${isActive ? "text-white" : "text-text-primary"}`}>{label}</span>
					</div>
					{disabled && <span className="ml-auto text-xs italic opacity-70">(Em breve)</span>}
				</button>
			);
		}

		return (
			<TabsTrigger
				key={value}
				value={value}
				disabled={disabled}
				className={`bg-secondary text-foreground data-[state=active]:bg-primary data-[state=active]:border-primary/20 rounded-controls flex items-center gap-2 border border-transparent px-3 py-2.5 text-sm font-medium transition-all duration-200 data-[state=active]:text-white data-[state=active]:shadow-md md:px-4 ${
					disabled
						? "border-border bg-muted text-muted-foreground hover:bg-muted cursor-not-allowed border-dashed opacity-60"
						: "hover:bg-primary/10 hover:text-primary hover:border-primary/20 hover:shadow-sm"
				} `}
				tabIndex={disabled ? -1 : 0}
				aria-disabled={disabled}
				title={disabled ? "Funcionalidade em breve" : label}
				onClick={() => !disabled && setActiveTab(value)}
				data-state={activeTab === value ? "active" : undefined}
			>
				<Icon className="size-4 flex-shrink-0" />
				<span className="whitespace-nowrap">{label}</span>
				{disabled && <span className="ml-1 hidden text-xs opacity-70 md:inline">(Em breve)</span>}
			</TabsTrigger>
		);
	};

	if (shouldUseMobileLayout) {
		return (
			<div className="mb-6 space-y-4">
				<div className="flex items-center justify-between gap-3">
					<Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
						<SheetTrigger asChild>
							<Button
								variant="outline"
								className="hover:border-primary/30 hover:bg-primary/5 border-border bg-background h-11 flex-1 justify-between border-2 transition-all duration-200"
							>
								<div className="flex items-center gap-2">
									{activeTabItem && <activeTabItem.icon className="size-4" />}
									<span className="font-medium">{activeTabItem?.label || "Selecionar"}</span>
								</div>
								<Menu className="text-muted-foreground size-4" />
							</Button>
						</SheetTrigger>
						<SheetContent side="bottom" className="rounded-t-controls bg-background border-border h-[80vh] px-0">
							<SheetHeader className="bg-muted/30 px-6 pb-4">
								<SheetTitle className="text-foreground text-left text-lg font-bold">Selecionar Seção</SheetTitle>
							</SheetHeader>
							<div className="grid gap-3 px-6 pb-6">
								{tabItems.map(item => (
									<ProtectedComponent action="read" subject={item.requiredPermission} key={item.value}>
										<TabButton key={item.value} item={item} variant="mobile" />
									</ProtectedComponent>
								))}
							</div>
						</SheetContent>
					</Sheet>
				</div>
			</div>
		);
	}

	return (
		<div className="relative flex h-auto w-full flex-col items-center justify-center py-2">
			<div className="flex h-full w-full flex-col items-start justify-between lg:flex-row lg:items-center lg:justify-between">
				<div className="w-full">
					<TabsList className="flex w-full flex-wrap gap-1 bg-transparent p-0 md:gap-2">
						{tabItems.map(item => (
							<ProtectedComponent action="read" subject={item.requiredPermission} key={item.value}>
								<TabButton key={item.value} item={item} />
							</ProtectedComponent>
						))}
					</TabsList>
				</div>
				<div className="flex w-full flex-col justify-end gap-3 sm:w-full sm:flex-row lg:w-auto lg:min-w-0 lg:flex-shrink-0"></div>
			</div>
		</div>
	);
};
