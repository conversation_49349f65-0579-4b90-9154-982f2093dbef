import { IExtractError<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IValidation<PERSON><PERSON><PERSON><PERSON><PERSON>, TErrorCategory } from "@/shared/types/requests/request.type";
import { AxiosError } from "axios";
import { CATEGORY_ERROR_MESSAGES, DEFAULT_ERROR_MESSAGES } from "./constants";

export class ErrorCategorizerService {
	public categorizeError(error: unknown): TErrorCategory {
		if (error instanceof AxiosError) return this.categorizeAxiosError(error);
		if (error instanceof Error) return this.categorizeGenericError(error);
		return "unknown";
	}

	private categorizeAxiosError(error: AxiosError): TErrorCategory {
		const status = error.response?.status;
		const code = error.code;
		if (!error.response || code === "NETWORK_ERROR" || code === "ERR_NETWORK" || code === "ECONNREFUSED" || code === "ENOTFOUND") return "network";
		if (code === "ECONNABORTED" || code === "ETIMEDOUT" || code === "ERR_CANCELED" || status === 408 || status === 504) return "timeout";
		if (status) {
			if (status === 401) return "authentication";
			if (status === 403) return "authorization";
			if (status === 422 || status === 400) return this.isValidationError(error.response?.data) ? "validation" : "business_logic";
			if (status >= 500) return "server_error";
			if (status >= 400 && status < 500) return "business_logic";
		}
		return "unknown";
	}

	private categorizeGenericError(error: Error): TErrorCategory {
		const message = error.message.toLowerCase();
		if (message.includes("network") || message.includes("connection")) return "network";
		if (message.includes("timeout") || message.includes("time")) return "timeout";
		if (message.includes("validation") || message.includes("invalid")) return "validation";
		if (message.includes("unauthorized") || message.includes("authentication")) return "authentication";
		if (message.includes("forbidden") || message.includes("permission")) return "authorization";
		return "unknown";
	}

	private isValidationError(data: unknown): boolean {
		if (!data || typeof data !== "object") return false;
		const errorData = data as Record<string, unknown>;
		return !!(
			errorData.errors ||
			errorData.validationErrors ||
			errorData.fieldErrors ||
			(Array.isArray(errorData.message) && errorData.message.length > 0) ||
			(typeof errorData.message === "string" && errorData.message.includes("validation"))
		);
	}
}

export class ErrorExtractionService {
	private categorizer = new ErrorCategorizerService();

	public extractErrorDetails(error: unknown): IExtractErrorDetailsResult {
		const category = this.categorizer.categorizeError(error);
		if (error instanceof AxiosError) return this.extractAxiosErrorDetails(error, category);
		if (error instanceof Error) return this.extractGenericErrorDetails(error, category);
		return {
			message: "Erro desconhecido",
			category: "unknown",
			originalError: error,
		};
	}

	private extractAxiosErrorDetails(error: AxiosError, category: TErrorCategory): IExtractErrorDetailsResult {
		const responseData = error.response?.data;
		const status = error.response?.status;
		const message = this.extractMessage(responseData) || this.getDefaultMessageForCategory(category, status);
		const fields = category === "validation" ? this.extractValidationFields(responseData) : undefined;
		const code = this.extractErrorCode(responseData) || error.code;
		const context = this.buildErrorContext(error, responseData);

		return {
			message,
			category,
			fields,
			code,
			originalError: error,
			context,
		};
	}

	private extractGenericErrorDetails(error: Error, category: TErrorCategory): IExtractErrorDetailsResult {
		return {
			message: error.message || this.getDefaultMessageForCategory(category),
			category,
			originalError: error,
			context: {
				name: error.name,
				stack: error.stack,
			},
		};
	}

	private extractMessage(data: unknown): string | null {
		if (!data || typeof data !== "object") return null;
		const errorData = data as Record<string, unknown>;
		if (typeof errorData.message === "string" && errorData.message.trim()) return errorData.message.trim();
		if (Array.isArray(errorData.message)) {
			const messages = errorData.message.filter(msg => typeof msg === "string" && msg.trim()).map(msg => msg.trim());
			if (messages.length > 0) return messages.join(". ");
		}

		if (typeof errorData.error === "string" && errorData.error.trim()) return errorData.error.trim();
		if (typeof errorData.detail === "string" && errorData.detail.trim()) return errorData.detail.trim();
		if (typeof errorData.description === "string" && errorData.description.trim()) return errorData.description.trim();
		if (Array.isArray(errorData.errors) && errorData.errors.length > 0) {
			const firstError = errorData.errors[0];
			if (typeof firstError === "string") return firstError;
			if (typeof firstError === "object" && firstError && typeof (firstError as Record<string, unknown>).message === "string") {
				return (firstError as Record<string, unknown>).message as string;
			}
		}

		return null;
	}

	private extractValidationFields(data: unknown): IValidationErrorField[] | undefined {
		if (!data || typeof data !== "object") return undefined;

		const errorData = data as Record<string, unknown>;
		const fields: IValidationErrorField[] = [];
		if (errorData.errors && typeof errorData.errors === "object" && !Array.isArray(errorData.errors)) {
			const errorsObj = errorData.errors as Record<string, unknown>;
			for (const [field, fieldError] of Object.entries(errorsObj)) {
				if (typeof fieldError === "string") {
					fields.push({ field, message: fieldError });
				} else if (Array.isArray(fieldError)) {
					const messages = fieldError.filter(msg => typeof msg === "string");
					if (messages.length > 0) {
						fields.push({ field, message: messages.join(". ") });
					}
				}
			}
		}

		if (Array.isArray(errorData.validationErrors)) {
			for (const validationError of errorData.validationErrors) {
				if (typeof validationError === "object" && validationError) {
					const ve = validationError as Record<string, unknown>;
					if (typeof ve.field === "string" && typeof ve.message === "string") {
						fields.push({
							field: ve.field,
							message: ve.message,
							code: typeof ve.code === "string" ? ve.code : undefined,
							value: ve.value,
						});
					}
				}
			}
		}

		if (errorData.fieldErrors && typeof errorData.fieldErrors === "object") {
			const fieldErrors = errorData.fieldErrors as Record<string, unknown>;
			for (const [field, fieldError] of Object.entries(fieldErrors)) {
				if (typeof fieldError === "string") {
					fields.push({ field, message: fieldError });
				}
			}
		}

		return fields.length > 0 ? fields : undefined;
	}

	private extractErrorCode(data: unknown): string | undefined {
		if (!data || typeof data !== "object") return undefined;
		const errorData = data as Record<string, unknown>;
		if (typeof errorData.code === "string") return errorData.code;
		if (typeof errorData.errorCode === "string") return errorData.errorCode;
		if (typeof errorData.error_code === "string") return errorData.error_code;
		return undefined;
	}

	private buildErrorContext(error: AxiosError, responseData: unknown): Record<string, unknown> {
		const context: Record<string, unknown> = {
			status: error.response?.status,
			statusText: error.response?.statusText,
			method: error.config?.method?.toUpperCase(),
			url: error.config?.url,
			baseURL: error.config?.baseURL,
		};

		if (error.response?.headers) context.responseHeaders = error.response.headers;
		if (error.config?.data && typeof error.config.data === "object") context.requestData = "[Request data available]";
		if (responseData && typeof responseData === "object") {
			const data = responseData as Record<string, unknown>;
			if (data.timestamp) context.timestamp = data.timestamp;
			if (data.path) context.path = data.path;
			if (data.traceId) context.traceId = data.traceId;
		}
		return context;
	}

	private getDefaultMessageForCategory(category: TErrorCategory, status?: number): string {
		const categoryMessage = CATEGORY_ERROR_MESSAGES[category];
		if (categoryMessage) return categoryMessage;
		if (status && DEFAULT_ERROR_MESSAGES[status]) return DEFAULT_ERROR_MESSAGES[status];
		return CATEGORY_ERROR_MESSAGES.unknown;
	}
}
