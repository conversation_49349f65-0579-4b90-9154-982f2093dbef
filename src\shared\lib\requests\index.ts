import { axiosInstance } from "@/config/api/instance";
import { ICreateRequest } from "@/shared/types/requests/create-request.type";
import { ApiResponse, IHandleMetadataAxiosRequestConfig } from "@/shared/types/requests/request.type";
import { AxiosError } from "axios";
import { handleGlobalError } from "../errors";
import { RequestCache } from "./cache/request-cache";
import { RequestManager } from "./client/request";
import { DEFAULT_RETRY_ATTEMPTS, DEFAULT_RETRY_DELAY, DEFAULT_TIMEOUT, RETRY_BACKOFF_FACTOR, RETRY_JITTER } from "./constants";
import { URLValidator } from "./security/url-validator";
export { RequestCache } from "./cache/request-cache";
export { RequestManager } from "./client/request";
export * from "./helpers/request-helpers";
export * from "./interceptors/request-interceptors";
export { URLValidator } from "./security/url-validator";

const urlValidator = new URLValidator();
const requestManager = new RequestManager();
const requestCache = new RequestCache();

function buildRequestKey(config: IHandleMetadataAxiosRequestConfig): string {
	const method = (config.method || "GET").toUpperCase();
	const uri = axiosInstance.getUri({
		...config,
		baseURL: config.baseURL || axiosInstance.defaults.baseURL,
	});
	if (method === "GET") return `${method}:${uri}`;
	const body = typeof config.data === "string" ? config.data : JSON.stringify(config.data ?? null);
	const bodyHash = hashString(body);
	return `${method}:${uri}:body@${bodyHash}`;
}

function hashString(input: string): string {
	let hash = 5381;
	for (let i = 0; i < input.length; i++) hash = (hash * 33) ^ input.charCodeAt(i);
	return (hash >>> 0).toString(36);
}

export async function createRequest<TSuccess, TRequest = unknown>(params: ICreateRequest<TRequest>): Promise<ApiResponse<TSuccess>> {
	try {
		const urlValidation = urlValidator.validateAndSanitizeURL(params.path);
		if (!urlValidation.isValid) {
			return {
				success: false,
				data: {
					message: urlValidation.reason || "URL inválida",
					method: params.method,
					url: params.path,
				},
				status: 400,
			};
		}

		const config: IHandleMetadataAxiosRequestConfig = {
			...params,
			url: urlValidation.sanitizedURL,
			method: params.method,
			data: params.body,
			timeout: params.timeout || DEFAULT_TIMEOUT,
			baseURL: params.baseURL || axiosInstance.defaults.baseURL,
		};

		if (params.method === "GET" && params.useCache === true) {
			const cacheKey = axiosInstance.getUri({ ...config });
			const cachedData = requestCache.get<TSuccess>(cacheKey);
			if (cachedData) {
				return {
					success: true,
					data: cachedData,
					status: 200,
				};
			}
		}

		const requestKey = buildRequestKey(config);
		const pendingRequest = requestManager.getPendingRequest(requestKey);
		if (pendingRequest) {
			return (await pendingRequest) as ApiResponse<TSuccess>;
		}
		const abortController = requestManager.createAbortController(requestKey);
		config.signal = abortController.signal;

		const promise = executeRequestWithRetry<TSuccess>(config, params);
		requestManager.registerPendingRequest(requestKey, promise);
		const response = await promise;
		if (response.success && params.method === "GET" && params.useCache === true) {
			const cacheKey = axiosInstance.getUri({ ...config });
			requestCache.set(cacheKey, response.data);
		}
		return response;
	} catch (error) {
		return handleGlobalError(error as Error);
	}
}

async function executeRequestWithRetry<TSuccess>(config: IHandleMetadataAxiosRequestConfig, params: ICreateRequest): Promise<ApiResponse<TSuccess>> {
	const maxRetries = params.retry !== false ? params.retryAttempts || DEFAULT_RETRY_ATTEMPTS : 0;
	let lastError: unknown;

	for (let attempt = 0; attempt <= maxRetries; attempt++) {
		try {
			const response = await requestManager.enqueue(() => axiosInstance.request<TSuccess>(config));
			return {
				success: true,
				data: response.data,
				status: response.status,
			};
		} catch (error: unknown) {
			lastError = error;
			if (error instanceof AxiosError && (error.code === "ERR_CANCELED" || error.message?.toLowerCase().includes("canceled"))) break;
			const status = error instanceof AxiosError ? error.response?.status : undefined;
			const code = error instanceof AxiosError ? error.code : undefined;
			const isTimeout = code === "ECONNABORTED" || code === "ETIMEDOUT" || status === 408 || status === 504;
			const isNetwork = code === "ERR_NETWORK" || code === "NETWORK_ERROR" || code === "ECONNREFUSED" || code === "ENOTFOUND" || !status;
			const isServerError = typeof status === "number" && status >= 500;
			const isRateLimited = status === 429;
			const isOther4xx = typeof status === "number" && status >= 400 && status < 500 && !isRateLimited && !isTimeout;
			if (isOther4xx) break;
			if (attempt >= maxRetries) break;
			let baseDelay = DEFAULT_RETRY_DELAY * Math.pow(RETRY_BACKOFF_FACTOR, attempt);
			if (error instanceof AxiosError && isRateLimited) {
				const retryAfterHeader = error.response?.headers?.["retry-after"] as string | undefined;
				const retryAfterMs = parseRetryAfter(retryAfterHeader);
				if (retryAfterMs !== null) baseDelay = Math.max(baseDelay, retryAfterMs);
			}
			const jitter = baseDelay * RETRY_JITTER * (Math.random() - 0.5);
			const delay = Math.round(baseDelay + jitter);

			if (isTimeout || isNetwork || isServerError || isRateLimited) {
				await new Promise(resolve => setTimeout(resolve, delay));
				continue;
			}

			break;
		}
	}

	return handleGlobalError(lastError);
}

function parseRetryAfter(headerValue?: string): number | null {
	if (!headerValue) return null;
	const seconds = Number(headerValue);
	if (!Number.isNaN(seconds)) return Math.max(0, seconds * 1000);
	const date = new Date(headerValue);
	const diff = date.getTime() - Date.now();
	return Number.isFinite(diff) ? Math.max(0, diff) : null;
}

export function cancelRequest(method: string, url: string): boolean {
	const requestKey = `${method.toUpperCase()}:${url}`;
	return requestManager.cancelRequest(requestKey);
}

async function executeRequestRawWithRetry<TSuccess, TRequest = unknown>(
	config: IHandleMetadataAxiosRequestConfig,
	params: ICreateRequest<TRequest>,
): Promise<ApiResponse<{ data: TSuccess; headers?: Record<string, unknown> }>> {
	const maxRetries = params.retry !== false ? params.retryAttempts || DEFAULT_RETRY_ATTEMPTS : 0;
	let lastError: unknown;

	for (let attempt = 0; attempt <= maxRetries; attempt++) {
		try {
			const response = await requestManager.enqueue(() => axiosInstance.request<TSuccess>(config));

			return {
				success: true,
				data: { data: response.data, headers: response.headers as Record<string, unknown> },
				status: response.status,
			};
		} catch (error: unknown) {
			lastError = error;

			if (error instanceof AxiosError && (error.code === "ERR_CANCELED" || error.message?.toLowerCase().includes("canceled"))) {
				break;
			}

			const status = error instanceof AxiosError ? error.response?.status : undefined;
			const code = error instanceof AxiosError ? error.code : undefined;
			const isTimeout = code === "ECONNABORTED" || code === "ETIMEDOUT" || status === 408 || status === 504;
			const isNetwork = code === "ERR_NETWORK" || code === "NETWORK_ERROR" || code === "ECONNREFUSED" || code === "ENOTFOUND" || !status;
			const isServerError = typeof status === "number" && status >= 500;
			const isRateLimited = status === 429;
			const isOther4xx = typeof status === "number" && status >= 400 && status < 500 && !isRateLimited && !isTimeout;
			if (isOther4xx) break;
			if (attempt >= maxRetries) break;

			let baseDelay = DEFAULT_RETRY_DELAY * Math.pow(RETRY_BACKOFF_FACTOR, attempt);
			if (error instanceof AxiosError && isRateLimited) {
				const retryAfterHeader = error.response?.headers?.["retry-after"] as string | undefined;
				const retryAfterMs = parseRetryAfter(retryAfterHeader);
				if (retryAfterMs !== null) baseDelay = Math.max(baseDelay, retryAfterMs);
			}
			const jitter = baseDelay * RETRY_JITTER * (Math.random() - 0.5);
			const delay = Math.round(baseDelay + jitter);

			if (isTimeout || isNetwork || isServerError || isRateLimited) {
				await new Promise(resolve => setTimeout(resolve, delay));
				continue;
			}

			break;
		}
	}

	return handleGlobalError(lastError) as ApiResponse<{ data: TSuccess; headers?: Record<string, unknown> }>;
}

export function clearCache(url?: string): void {
	if (url) {
		requestCache.delete(url);
	} else {
		requestCache.clear();
	}
}

export async function createRawRequest<TSuccess, TRequest = unknown>(
	params: ICreateRequest<TRequest>,
): Promise<ApiResponse<{ data: TSuccess; headers?: Record<string, unknown> }>> {
	try {
		const urlValidation = urlValidator.validateAndSanitizeURL(params.path);
		if (!urlValidation.isValid) {
			return {
				success: false,
				data: { message: urlValidation.reason || "URL inválida" },
				status: 400,
			};
		}

		const config: IHandleMetadataAxiosRequestConfig = {
			...params,
			url: urlValidation.sanitizedURL,
			method: params.method,
			data: params.body,
			timeout: params.timeout || DEFAULT_TIMEOUT,
			baseURL: params.baseURL || axiosInstance.defaults.baseURL,
		};
		const requestKey = buildRequestKey(config);
		const pendingRequest = requestManager.getPendingRequest(requestKey);
		if (pendingRequest) {
			return (await pendingRequest) as ApiResponse<{ data: TSuccess; headers?: Record<string, unknown> }>;
		}
		const abortController = requestManager.createAbortController(requestKey);
		config.signal = abortController.signal;

		const promise = (async () => {
			const result = await executeRequestRawWithRetry<TSuccess, TRequest>(config, params);
			return result;
		})();
		requestManager.registerPendingRequest(requestKey, promise);
		const response = await promise;
		return response;
	} catch (error) {
		return handleGlobalError(error as Error) as ApiResponse<{ data: TSuccess; headers?: Record<string, unknown> }>;
	}
}
