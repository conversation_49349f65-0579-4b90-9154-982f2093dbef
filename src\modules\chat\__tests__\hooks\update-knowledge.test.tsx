"use client";

import { createPatchRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";
import { useEditKnowledgeMutation } from "../../hooks/edit/mutation.hook";

jest.mock("@/shared/lib/requests", () => ({
	createPatchRequest: jest.fn(),
}));
jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));
jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canUpdate: () => true })),
}));

const mockedCreatePatchRequest = createPatchRequest as jest.MockedFunction<typeof createPatchRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Conhecimento editado com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Error ao editar o conhecimento" };

describe("useEditKnowledgeMutation", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve editar o conhecimento com sucesso", async () => {
		mockedCreatePatchRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useEditKnowledgeMutation({}), { wrapper });
		await expect(
			result.current.mutate({
				id: "123",
				form: {
					title: "Título atualizado",
					content: "Conteúdo atualizado",
					isActive: true,
				},
			}),
		).resolves.toEqual(mockSuccess);
	});

	it("deve falhar ao editar o conhecimento", async () => {
		mockedCreatePatchRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useEditKnowledgeMutation({}), { wrapper });
		await expect(
			result.current.mutate({
				id: "123",
				form: {
					title: "Título atualizado",
					content: "Conteúdo atualizado",
					isActive: true,
				},
			}),
		).rejects.toThrow("Error ao editar o conhecimento");
	});
});
