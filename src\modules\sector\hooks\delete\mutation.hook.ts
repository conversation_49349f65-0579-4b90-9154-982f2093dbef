"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { SECTOR_ENDPOINTS } from "../../api/endpoints";
import { sectorQueryKeys } from "../../constants/query";
import { SECTOR_SUBJECTS } from "../../constants/subjects";

export const useDeleteSectorMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: sectorQueryKeys.custom("delete"),
			endpoint: (id: string) => SECTOR_ENDPOINTS.DELETE(id),
			subject: SECTOR_SUBJECTS.SECTOR,
			type: "delete",
			messages: {
				loading: "Excluindo setor...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: sectorQueryKeys,
		},
		{ ...params },
	);

	return {
		...mutation,
	};
};
