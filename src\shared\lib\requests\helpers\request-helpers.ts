import { ICreateRequest } from "@/shared/types/requests/create-request.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { toast } from "@/core/toast";
import { createRequest } from "../index";

type HelperRequestParams<T> = Omit<ICreateRequest<T>, "method">;

const isClientSide = (): boolean => typeof window !== "undefined";

const normalizeResponse = <T>(response: ApiResponse<T>): ApiResponse<T> => ({
	success: response.success,
	data: JSON.parse(JSON.stringify(response.data)),
	status: response.status,
});

async function callInternalApi<T>(payload: unknown): Promise<ApiResponse<T>> {
    const res = await fetch("/routes/request", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
        credentials: "include",
    });
    const data = (await res.json()) as ApiResponse<T>;
    try {
        const anyData = data as unknown as { success: boolean; status?: number; data?: { message?: string } };
        if (anyData && anyData.success === false && anyData.status === 401) {
            const message = anyData.data?.message || "Sua sessão expirou - faça login novamente";
            toast.error(message, { duration: 6000 });
        }
    } catch {}
    return data;
}

export const createGetRequest = async <TSuccess>(path: string, options?: Omit<HelperRequestParams<never>, "path" | "body">): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return callInternalApi<TSuccess>({ method: "GET", path, options });
	const response = await createRequest<TSuccess>({ ...options, path, method: "GET" });
	return normalizeResponse(response);
};

export const createPostRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return callInternalApi<TSuccess>({ method: "POST", path, body, options });
	const response = await createRequest<TSuccess, TRequest>({ ...options, path, method: "POST", body });
	return normalizeResponse(response);
};

export const createPutRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return callInternalApi<TSuccess>({ method: "PUT", path, body, options });
	const response = await createRequest<TSuccess, TRequest>({ ...options, path, method: "PUT", body });
	return normalizeResponse(response);
};

export const createPatchRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return callInternalApi<TSuccess>({ method: "PATCH", path, body, options });
	const response = await createRequest<TSuccess, TRequest>({ ...options, path, method: "PATCH", body });
	return normalizeResponse(response);
};

export const createDeleteRequest = async <TSuccess>(
	path: string,
	options?: Omit<HelperRequestParams<never>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return callInternalApi<TSuccess>({ method: "DELETE", path, options });
	const response = await createRequest<TSuccess>({ ...options, path, method: "DELETE" });
	return normalizeResponse(response);
};

export const createBatchRequestsRequest = async <T extends readonly unknown[]>(requests: { [K in keyof T]: () => Promise<T[K]> }): Promise<{
	[K in keyof T]: T[K] | Error;
}> => {
	const results = await Promise.allSettled(requests.map(request => request()));
	return results.map(result => (result.status === "fulfilled" ? result.value : result.reason)) as { [K in keyof T]: T[K] | Error };
};

export const createWithTimeoutRequest = async <TSuccess, TRequest = unknown>(
	params: ICreateRequest<TRequest>,
	timeoutMs: number,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) {
		return callInternalApi<TSuccess>({
			method: params.method,
			path: params.path,
			body: params.body,
			options: { ...params, timeout: timeoutMs },
		});
	}

	const response = await createRequest<TSuccess, TRequest>({ ...params, timeout: timeoutMs });
	return normalizeResponse(response);
};
