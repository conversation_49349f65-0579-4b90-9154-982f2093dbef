"use client";

import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useCloneFormMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, string> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, string>(
		{
			mutationKey: inspectionKeys.forms.custom("clone"),
			endpoint: id => INSPECTION_FORM_ENDPOINTS.CLONE(id),
			subject: INSPECTION_SUBJECTS.INSPECTION_FORM,
			type: "create",
			messages: {
				loading: "Clonando formul<PERSON>rio...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.forms,
		},
		{ ...params },
	);

	return { ...mutation };
};
