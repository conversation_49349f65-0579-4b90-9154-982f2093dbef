import useCreateCollabSectorForm from "@/modules/inspection/hooks/collaborator-by-sector/create/collab-sector.hook";
import { useUpdateCollabBySectorMutation } from "@/modules/inspection/hooks/collaborator-by-sector/edit/mutation.hook";
import { useCollabBySectorFindById } from "@/modules/inspection/hooks/collaborator-by-sector/list/find-by-id.hook";
import { TCreateCollabSectorSchema } from "@/modules/inspection/validators/collaborator-by-sector/create";
import { FormLoading } from "@/shared/components/custom/loading";
import { Modal } from "@/shared/components/custom/modal";
import FormCreateCollabBysector from "../create/form";
import { useEffect } from "react";

interface IModalEditCollabByProps {
	isOpen: boolean;
	onClose: () => void;
	formId: string;
	canEdit: boolean;
}
export function ModalEditCollabBySector({ isOpen, onClose, formId, canEdit }: IModalEditCollabByProps) {
	const { data, isLoading, hasError, error } = useCollabBySectorFindById(formId, isOpen);
	const { methods } = useCreateCollabSectorForm();
	function handleClose() {
		onClose();
	}
	const { mutate } = useUpdateCollabBySectorMutation({ onClose: handleClose });

	useEffect(() => {
		if (!isOpen || !data) return;
		methods.reset({
			sector: {
				id: data.sectorId,
				name: data.sectorName,
			},
			collaborator: {
				id: data.collaboratorDocument,
				name: data.collaboratorName,
			},
			mode: "edit",
		});
	}, [data, methods, isOpen]);

	function handleSubmit(formData: TCreateCollabSectorSchema) {
		mutate({
			form: {
				sectorId: Number(formData.sector.id),
			},
			id: formId,
		});
	}
	return (
		<Modal isOpen={isOpen} onClose={handleClose} className="!w-[500px]" title={canEdit ? "Edição de Formulário" : "Visualização de Formulário"}>
			{isLoading && <FormLoading message="Carregando dados do formulário..." variant="spinner" />}
			{hasError && (
				<div className="flex items-center justify-center p-8">
					<div className="space-y-2 text-center">
						<p className="text-destructive font-medium">Erro ao carregar formulário</p>
						<p className="text-muted-foreground text-sm">{error}</p>
					</div>
				</div>
			)}
			{data && <FormCreateCollabBysector onClose={handleClose} methods={methods} onSubmit={handleSubmit} isEdit={true} />}
		</Modal>
	);
}
