import { Modal } from "../../../../../../../shared/components/custom/modal";
import { useCreateCellByProductTypeForm } from "../../../../../hooks/cell-by-product-type/create/form.hook";
import { useCreateCellByProductTypeMutation } from "../../../../../hooks/cell-by-product-type/create/mutation.hook";
import { CellByProductTypeToCreateMapper } from "../../../../../lib/mappers/cells-by-product-types/cell-by-product-type-to-create.mapper";
import { TCreateCellByProductTypeForm } from "../../../../../validators/cell-by-product-type";
import { FormCreateCellByProductType } from "./form";

interface ModalCreateCellByProductTypeProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateCellByProductType = ({ isOpen, onClose }: ModalCreateCellByProductTypeProps) => {
	const methods = useCreateCellByProductTypeForm();
	const handleClose = () => {
		onClose();
		methods.reset();
	};
	const { mutate } = useCreateCellByProductTypeMutation({ onClose: handleClose });
	const handleSubmit = async (data: TCreateCellByProductTypeForm) => await mutate(CellByProductTypeToCreateMapper.map(data));

	return (
		<Modal isOpen={isOpen} onClose={handleClose} title="Vínculo de célula por produto tipo">
			<FormCreateCellByProductType methods={methods} onSubmit={handleSubmit} onClose={handleClose} />
		</Modal>
	);
};
