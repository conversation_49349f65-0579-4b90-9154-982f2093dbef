"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { PRODUCTION_SCRIPT_ENDPOINTS } from "../../api/endpoints";
import { productionScriptQueryKeys } from "../../constants/query";
import { PRODUCTION_SCRIPT_SUBJECTS } from "../../constants/subjects";
import { IProductionScriptUpdateDto } from "../../types/dtos/update.dto";

type TProductionScriptParams = { id: string; form: IProductionScriptUpdateDto };

export const useEditProductionScriptMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, TProductionScriptParams> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, TProductionScriptParams>(
		{
			mutationKey: productionScriptQueryKeys.custom("edit"),
			endpoint: ({ id }) => PRODUCTION_SCRIPT_ENDPOINTS.UPDATE(id),
			subject: PRODUCTION_SCRIPT_SUBJECTS.PRODUCTION_SCRIPT,
			type: "put",
			messages: {
				loading: "Editando roteiro de produção...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: productionScriptQueryKeys,
			transformRequest: ({ form }) => form,
		},
		{ ...params },
	);

	return mutation;
};
