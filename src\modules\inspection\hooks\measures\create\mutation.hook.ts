"use client";

import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { ICreateMeasuresDTO } from "@/modules/inspection/types/measures/create-measures.dto";
import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";

export const useCreateMeasureMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, ICreateMeasuresDTO> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, ICreateMeasuresDTO>(
		{
			mutationKey: inspectionKeys.measures.custom("create"),
			endpoint: MEASURES_ENDPOINTS.CREATE,
			subject: "all",
			type: "create",
			messages: {
				loading: "Criando medida...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.measures,
		},
		{ ...params },
	);

	return { ...mutation };
};
