"use client";

import { MODAL_CONTENT_ID } from "@/shared/components/custom/modal";
import { DragEndEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { useAtom } from "jotai";
import { useCallback } from "react";
import { activityConditionsAtom } from "../../atoms/conditions.atom";
import { ACTIVITY_CONDITION_OPERATORS, ACTIVITY_LOGIC_OPERATORS } from "../../constants/activity";
import { IAcitivityCondition } from "../../types/create.dto";

export const useConditionsEditor = () => {
	const [conditions, setConditions] = useAtom(activityConditionsAtom);

	const operatorOptions = Object.keys(ACTIVITY_CONDITION_OPERATORS) as (keyof typeof ACTIVITY_CONDITION_OPERATORS)[];
	const logicOptions = Object.keys(ACTIVITY_LOGIC_OPERATORS) as string[];

	const addCondition = useCallback(() => {
		setConditions((prev: IAcitivityCondition[]) => [
			...prev,
			{ sequence: prev.length, description: "", field: "", operator: operatorOptions[0], value: "", logic: [] },
		]);
		setTimeout(() => {
			const modal = document.getElementById(MODAL_CONTENT_ID);
			if (modal) modal.scrollTo({ top: modal.scrollHeight, behavior: "smooth" });
		}, 10);
	}, [operatorOptions, setConditions]);

	const removeCondition = (index: number) =>
		setConditions((prev: IAcitivityCondition[]) => prev.filter((_, i) => i !== index).map((c, i) => ({ ...c, sequence: i })));

	const updateCondition = <K extends keyof IAcitivityCondition>(index: number, key: K, value: IAcitivityCondition[K]) => {
		setConditions((prev: IAcitivityCondition[]) => {
			const next = [...prev];
			const curr = next[index];
			if (!curr) return prev;
			next[index] = { ...curr, [key]: value };
			return next;
		});
	};

	const onDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;
		if (!over || active.id === over.id) return;
		const oldIndex = Number(active.id);
		const newIndex = Number(over.id);
		const reordered = arrayMove(conditions, oldIndex, newIndex).map((c, i) => ({ ...c, sequence: i }));
		setConditions(reordered);
	};

	const inferInputType = (fieldId: string): "text" | "number" | "date" => {
		const id = (fieldId || "").toLowerCase();
		if (/data|date/.test(id)) return "date";
		if (/qtd|quant|num|n[uú]mero|valor|pre[cç]o|price|amount|total|percent|%/.test(id)) return "number";
		return "text";
	};

	return {
		conditions,
		addCondition,
		removeCondition,
		updateCondition,
		onDragEnd,
		inferInputType,
		operatorOptions,
		logicOptions,
	};
};
