import { IAcitivityCondition, IActivityField, ICreateActivityDto } from "../../types/create.dto";
import { TCreateActivity } from "../../validators/create";
import { normalizeConditionInput, normalizeFieldInput, sortBySequence, uniqueBy } from "./utils";

export const mapToCreateActivityDto = (
  form: TCreateActivity,
  opts?: { fields?: IActivityField[]; conditions?: IAcitivityCondition[] },
): ICreateActivityDto => {
	const name = form.name.trim();

	const fieldsInput = opts?.fields ?? [];
	const conditionsInput = opts?.conditions ?? [];

	const fields = sortBySequence(
    uniqueBy((fieldsInput ?? []).map(f => normalizeFieldInput(f)), f => f.field),
  );

	const conditions = sortBySequence(
    (conditionsInput ?? [])
      .map(c => normalizeConditionInput(c))
      .filter(c => c.description && c.field),
  );

	return { name, fields, conditions } satisfies ICreateActivityDto;
};
