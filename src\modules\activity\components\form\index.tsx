"use client";

import { requiredLabel } from "@/modules/inspection/components/register/tabs/forms/form-item/form";
import { FormLoading } from "@/shared/components/custom/loading";
import { Modal } from "@/shared/components/custom/modal";
import { Badge } from "@/shared/components/shadcn/badge";
import { Button } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/shared/components/shadcn/tabs";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { useAtomValue } from "jotai";
import { Activity, FileText, Filter, Save, X } from "lucide-react";
import { useState } from "react";
import type { UseFormReturn } from "react-hook-form";
import { activityConditionsAtom } from "../../atoms/conditions.atom";
import { activityFields<PERSON>tom } from "../../atoms/fields.atom";
import { useActivityFormGuards } from "../../hooks/form/guards.hook";
import type { TCreateActivity } from "../../validators/create";
import { ConditionsEditor } from "./conditions-editor";
import { FieldsEditor } from "./fields-editor";

interface ActivityFormModalProps {
	isOpen: boolean;
	title: string;
	description?: string;
	form: UseFormReturn<TCreateActivity>;
	isSubmitting?: boolean;
	loading?: boolean;
	onSubmit: (data: TCreateActivity) => void;
	onCancel: () => void;
}

export const ActivityFormModal = ({ isOpen, title, form, isSubmitting, loading, onSubmit, onCancel }: ActivityFormModalProps) => {
	const [activeTab, setActiveTab] = useState("dados");
	const conditions = useAtomValue(activityConditionsAtom);
	const fields = useAtomValue(activityFieldsAtom);
	const { hasDuplicateFields, hasNoFields, hasEmptyFieldRows, conditionErrors, getCanSubmit } = useActivityFormGuards();
	const canSubmit = getCanSubmit(isSubmitting);

	// Contadores para badges
	const fieldsCount = fields.length;
	const conditionsCount = conditions.length;

	// Validações por seção
	const dadosErrors = form.formState.errors.name;
	const fieldsErrors = hasNoFields || hasEmptyFieldRows || hasDuplicateFields;
	const conditionsErrors = conditions.length === 0 || conditionErrors.emptyRows || conditionErrors.missingLogic;

	return (
		<Modal isOpen={isOpen} onClose={onCancel} title={title} className="!h-[95vh] !w-full !max-w-[95vw] md:!h-auto md:!w-[1400px] md:!max-w-none">
			<div className="relative flex h-full flex-col overflow-hidden">
				{loading && <FormLoading message="Carregando dados da atividade..." variant="full" />}
				{!loading && (
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="flex h-full flex-col">
							<Tabs value={activeTab} onValueChange={setActiveTab} className="flex h-full flex-col">
								{/* Header com Tabs */}
								<div className="bg-muted/30 border-b px-6 py-4">
									<TabsList className="grid w-full grid-cols-3">
										<TabsTrigger value="dados" className="flex items-center gap-2">
											<Activity className="size-4" />
											Dados da Atividade
											{dadosErrors && <Badge variant="destructive" className="ml-1 size-2 p-0" />}
										</TabsTrigger>
										<TabsTrigger value="campos" className="flex items-center gap-2">
											<FileText className="size-4" />
											Campos
											{fieldsCount > 0 && <Badge variant="secondary">{fieldsCount}</Badge>}
											{fieldsErrors && <Badge variant="destructive" className="ml-1 size-2 p-0" />}
										</TabsTrigger>
										<TabsTrigger value="condicoes" className="flex items-center gap-2">
											<Filter className="size-4" />
											Condições
											{conditionsCount > 0 && <Badge variant="secondary">{conditionsCount}</Badge>}
											{conditionsErrors && <Badge variant="destructive" className="ml-1 size-2 p-0" />}
										</TabsTrigger>
									</TabsList>
								</div>

								{/* Content */}
								<div className="flex-1 overflow-hidden">
									{/* Seção 1: Dados da Atividade */}
									<TabsContent value="dados" className="h-full overflow-y-auto p-6">
										<div className="mx-auto max-w-2xl space-y-6">
											<div className="space-y-2">
												<h3 className="text-lg font-semibold">Informações Básicas</h3>
												<p className="text-muted-foreground text-sm">Configure o nome e descrição da atividade que será criada.</p>
											</div>

											<div className="space-y-4">
												<FormField
													control={form.control}
													name="name"
													render={({ field }) => (
														<FormItem>
															<FormLabel>{requiredLabel("Nome da atividade")}</FormLabel>
															<FormControl>
																<Input
																	autoFocus
																	autoComplete="off"
																	placeholder="Digite o nome da atividade"
																	{...field}
																	className="focus:border-primary/50 w-full"
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormItem>
													<FormLabel>Descrição (opcional)</FormLabel>
													<FormControl>
														<Textarea placeholder="Descreva o propósito desta atividade..." className="min-h-[100px] resize-none" />
													</FormControl>
													<p className="text-muted-foreground text-xs">
														Esta descrição ajudará outros usuários a entenderem o objetivo da atividade.
													</p>
												</FormItem>
											</div>
										</div>
									</TabsContent>

									{/* Seção 2: Campos */}
									<TabsContent value="campos" className="h-full overflow-y-auto p-6">
										<div className="space-y-6">
											<div className="space-y-2">
												<h3 className="text-lg font-semibold">Configuração de Campos</h3>
												<p className="text-muted-foreground text-sm">Defina quais campos serão exibidos na atividade e em que ordem.</p>
											</div>
											<FieldsEditor />
										</div>
									</TabsContent>

									{/* Seção 3: Condições */}
									<TabsContent value="condicoes" className="h-full overflow-y-auto p-6">
										<div className="space-y-6">
											<div className="space-y-2">
												<h3 className="text-lg font-semibold">Lógica de Condições</h3>
												<p className="text-muted-foreground text-sm">
													Configure as regras que determinam quando esta atividade deve ser executada.
												</p>
											</div>
											<ConditionsEditor />

											{/* Preview da lógica */}
											{conditions.length > 0 && (
												<div className="bg-muted/50 rounded-lg border p-4">
													<h4 className="mb-2 text-sm font-medium">Preview da Lógica:</h4>
													<div className="text-muted-foreground font-mono text-sm">
														{conditions.map((condition, index) => (
															<span key={index}>
																({condition.field} {condition.operator} "{condition.value}")
																{index < conditions.length - 1 && condition.logic?.[0] && (
																	<span className="text-primary font-semibold"> {condition.logic[0]} </span>
																)}
															</span>
														))}
													</div>
												</div>
											)}
										</div>
									</TabsContent>
								</div>

								{/* Footer */}
								<div className="bg-muted/30 border-t px-6 py-4">
									<div className="flex flex-col gap-3">
										{/* Mensagens de erro */}
										{hasNoFields && <span className="text-destructive text-sm">Adicione pelo menos um campo.</span>}
										{hasEmptyFieldRows && <span className="text-destructive text-sm">Preencha Nome e Campo em todos os itens.</span>}
										{hasDuplicateFields && <span className="text-destructive text-sm">Resolva campos duplicados.</span>}
										{conditions.length === 0 && <span className="text-destructive text-sm">Adicione pelo menos uma condição.</span>}
										{conditionErrors.emptyRows && (
											<span className="text-destructive text-sm">Preencha todos os campos de cada condição.</span>
										)}
										{conditionErrors.missingLogic && (
											<span className="text-destructive text-sm">Selecione o operador lógico entre as condições.</span>
										)}

										{/* Botões */}
										<div className="flex justify-between">
											<Button type="button" variant="ghost" onClick={onCancel}>
												<X className="size-4" />
												Cancelar
											</Button>
											<Button type="submit" disabled={!canSubmit} className="flex items-center gap-2">
												<Save className="size-4" />
												{isSubmitting ? "Salvando..." : "Salvar Atividade"}
											</Button>
										</div>
									</div>
								</div>
							</Tabs>
						</form>
					</Form>
				)}
			</div>
		</Modal>
	);
};

export default ActivityFormModal;
