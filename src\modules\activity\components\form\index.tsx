"use client";

import { requiredLabel } from "@/modules/inspection/components/register/tabs/forms/form-item/form";
import { FormLoading } from "@/shared/components/custom/loading";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { useAtomValue } from "jotai";
import { Save, X } from "lucide-react";
import type { UseFormReturn } from "react-hook-form";
import { activityConditionsAtom } from "../../atoms/conditions.atom";
import { useActivityFormGuards } from "../../hooks/form/guards.hook";
import type { TCreateActivity } from "../../validators/create";
import { ConditionsEditor } from "./conditions-editor";
import { FieldsEditor } from "./fields-editor";

interface ActivityFormModalProps {
	isOpen: boolean;
	title: string;
	description?: string;
	form: UseFormReturn<TCreateActivity>;
	isSubmitting?: boolean;
	loading?: boolean;
	onSubmit: (data: TCreateActivity) => void;
	onCancel: () => void;
}

export const ActivityFormModal = ({ isOpen, title, form, isSubmitting, loading, onSubmit, onCancel }: ActivityFormModalProps) => {
	const conditions = useAtomValue(activityConditionsAtom);
	const { hasDuplicateFields, hasNoFields, hasEmptyFieldRows, conditionErrors, getCanSubmit } = useActivityFormGuards();
	const canSubmit = getCanSubmit(isSubmitting);

	return (
		<Modal isOpen={isOpen} onClose={onCancel} title={title} className="!h-[95vh] !w-full !max-w-[95vw] md:!h-auto md:!w-[1400px] md:!max-w-none">
			<div className="relative space-y-6 overflow-hidden">
				{loading && <FormLoading message="Carregando dados da atividade..." variant="full" />}
				{!loading && (
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{requiredLabel("Nome da atividade")}</FormLabel>
										<FormControl>
											<Input
												autoFocus
												autoComplete="off"
												placeholder="Digite o nome da atividade"
												{...field}
												className="focus:border-primary/50 w-full"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FieldsEditor />
							<ConditionsEditor />

							<div className="flex flex-col gap-3">
								{hasNoFields ? <span className="text-destructive text-sm">Adicione pelo menos um campo.</span> : null}
								{hasEmptyFieldRows ? <span className="text-destructive text-sm">Preencha Nome e Campo em todos os itens.</span> : null}
								{hasDuplicateFields ? <span className="text-destructive text-sm">Resolva campos duplicados.</span> : null}
								{conditions.length === 0 ? <span className="text-destructive text-sm">Adicione pelo menos uma condição.</span> : null}
								{conditionErrors.emptyRows ? (
									<span className="text-destructive text-sm">Preencha todos os campos de cada condição.</span>
								) : null}
								{conditionErrors.missingLogic ? (
									<span className="text-destructive text-sm">Selecione o operador lógico entre as condições.</span>
								) : null}

								<div className="flex justify-end gap-3">
									<Button type="button" variant="ghost" onClick={onCancel}>
										<X className="size-4" />
										Cancelar
									</Button>
									<Button type="submit" disabled={!canSubmit} className="flex items-center gap-2">
										<Save className="size-4" />
										{isSubmitting ? "Salvando..." : "Salvar"}
									</Button>
								</div>
							</div>
						</form>
					</Form>
				)}
			</div>
		</Modal>
	);
};

export default ActivityFormModal;
