import { NextRequest, NextResponse } from "next/server";
import { UnifiedAuthMiddleware } from "./config/middleware/auth/unified-auth.middleware";
import { generateBase64Nonce, isHtmlDocumentRequest } from "./config/middleware/security/nonce.util";
import { SecurityHeadersMiddleware } from "./config/middleware/security/security-headers.middleware";

export async function middleware(request: NextRequest): Promise<Response> {
	try {
		const isDocument = isHtmlDocumentRequest(request);
		const nonce = isDocument ? generateBase64Nonce() : undefined;
		const authResponse = await UnifiedAuthMiddleware.process(request);
		if (authResponse.status >= 300 && authResponse.status < 400) return SecurityHeadersMiddleware.addToResponse(authResponse, nonce);
		const next = NextResponse.next({
			request: {
				headers: nonce
					? new Headers({
							...Object.fromEntries(request.headers),
							"x-nonce": nonce,
						})
					: undefined,
			},
		});
		if (nonce) next.headers.set("x-nonce", nonce);
		return SecurityHeadersMiddleware.addToResponse(next, nonce);
	} catch (error) {
		console.error("Erro no middleware principal:", error);
		const errorResponse = NextResponse.redirect(new URL("/routes/login", request.url), { status: 302 });
		return SecurityHeadersMiddleware.addToResponse(errorResponse);
	}
}

export const config = {
	matcher: ["/((?!api|_next/static|_next/image|_next/font|favicon.ico|events/devtools/events|events$|\\.well-known).*)"],
};
