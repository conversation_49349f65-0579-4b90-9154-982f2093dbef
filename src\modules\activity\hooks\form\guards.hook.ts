"use client";

import { useAtomValue } from "jotai";
import { activityConditionsAtom } from "../../atoms/conditions.atom";
import { activityFieldsAtom } from "../../atoms/fields.atom";

export const useActivityFormGuards = () => {
  const fields = useAtomValue(activityFieldsAtom);
  const conditions = useAtomValue(activityConditionsAtom);

  const hasDuplicateFields = (() => {
    const counts = new Map<string, number>();
    for (const f of fields) {
      const key = (f.field ?? "").trim().toLowerCase();
      if (!key) continue;
      counts.set(key, (counts.get(key) ?? 0) + 1);
    }
    for (const [, c] of counts) if (c > 1) return true;
    return false;
  })();

  const hasNoFields = fields.length === 0;
  const hasEmptyFieldRows = fields.some(f => !String(f.name ?? "").trim() || !String(f.field ?? "").trim());

  const conditionErrors = (() => {
    if (conditions.length === 0) return { hasAny: true, missingLogic: false, emptyRows: true };
    let missingLogic = false;
    let emptyRows = false;
    for (let i = 0; i < conditions.length; i++) {
      const c = conditions[i];
      const baseEmpty = !String(c.description ?? "").trim() || !String(c.field ?? "").trim() || !String(c.operator ?? "").trim() || !String(c.value ?? "").trim();
      if (baseEmpty) emptyRows = true;
      if (i < conditions.length - 1 && !(c.logic && c.logic[0])) missingLogic = true;
    }
    return { hasAny: missingLogic || emptyRows, missingLogic, emptyRows };
  })();

  const getCanSubmit = (isSubmitting?: boolean) => !isSubmitting && !hasDuplicateFields && !hasNoFields && !hasEmptyFieldRows && !conditionErrors.hasAny;

  return {
    hasDuplicateFields,
    hasNoFields,
    hasEmptyFieldRows,
    conditionErrors,
    getCanSubmit,
  };
};

