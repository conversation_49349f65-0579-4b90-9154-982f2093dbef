import { IActivityConditionOperator, IActivityLogicOperator } from "../constants/activity";

export interface ICreateActivityDto {
	name: string;
	conditions: IAcitivityCondition[];
	fields: IActivityField[];
}

export interface IActivityField {
	name: string;
	field: string;
	sequence: number;
}

export interface IAcitivityCondition {
	id?: number;
	sequence: number;
	description: string;
	field: string;
	operator: IActivityConditionOperator;
	value: string;
	logic: IActivityLogicOperator[] | null;
}
