import { Button } from "@/shared/components/shadcn/button";
import { DraggableAttributes } from "@dnd-kit/core";
import { SyntheticListenerMap } from "@dnd-kit/core/dist/hooks/utilities";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical } from "lucide-react";
import React from "react";
import { useGroupDragContext } from "../group/drag-context";

interface DragHandleProps {
	id: string;
	isOnlyItem?: boolean;
	groupDragProps?: {
		attributes: DraggableAttributes;
		listeners: SyntheticListenerMap | undefined;
	};
}

export const DragHandle: React.FC<DragHandleProps> = ({ id, isOnlyItem: propIsOnlyItem = false, groupDragProps: propGroupDragProps }) => {
	const { groupDragProps, isOnlyItem: contextIsOnlyItem } = useGroupDragContext();
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id,
		disabled: propIsOnlyItem || contextIsOnlyItem,
	});

	const finalGroupDragProps = propGroupDragProps || groupDragProps;
	const finalIsOnlyItem = propIsOnlyItem || contextIsOnlyItem;
	const dragProps = finalIsOnlyItem && finalGroupDragProps ? finalGroupDragProps : { attributes, listeners };

	return (
		<div
			ref={setNodeRef}
			style={{ transform: CSS.Transform.toString(transform), transition }}
			className={`${isDragging && !finalIsOnlyItem ? "opacity-50" : ""}`}
		>
			<Button
				type="button"
				{...dragProps.attributes}
				{...dragProps.listeners}
				variant="ghost"
				size="icon"
				className="text-muted-foreground size-7 cursor-grab hover:bg-transparent active:cursor-grabbing"
			>
				<GripVertical className="text-muted-foreground size-3" />
				<span className="sr-only">{finalIsOnlyItem ? "Arraste para reordenar grupo" : "Arraste para reordenar item"}</span>
			</Button>
		</div>
	);
};
