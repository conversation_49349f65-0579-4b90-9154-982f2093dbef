import { Modal } from "@/shared/components/custom/modal";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Save, X } from "lucide-react";
import { Checkbox } from "../../../../shared/components/shadcn/checkbox";
import { requiredLabel } from "../../../inspection/components/register/tabs/forms/form-item/form";
import { useCreateProductTypeForm } from "../../hooks/create/form.hook";
import { useCreateProductTypeMutation } from "../../hooks/create/mutation.hook";

interface CreateProductTypeModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const CreateProductTypeModal = ({ isOpen, onClose }: CreateProductTypeModalProps) => {
	const form = useCreateProductTypeForm();

	const handleClose = () => {
		form.reset();
		onClose();
	};

	const { mutate, isLoading } = useCreateProductTypeMutation({ onClose: handleClose });

	return (
		<Modal
			isOpen={isOpen}
			onClose={handleClose}
			title="Adicionar Novo Tipo de Produto"
			description="Crie um novo tipo de produto"
			size="xl"
			className="max-h-[90vh]"
		>
			<div className="space-y-6">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(data => mutate(data))} className="space-y-4">
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{requiredLabel("Nome")}</FormLabel>
									<FormControl>
										<Input
											autoFocus
											autoComplete="off"
											placeholder="Digite o nome do tipo de produto..."
											{...field}
											className="focus:border-primary/50"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="factoryStatus"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center space-y-0 space-x-3">
										<FormControl>
											<Checkbox checked={field.value} onCheckedChange={field.onChange} className="focus:border-primary/50" />
										</FormControl>
										<FormLabel className="cursor-pointer text-sm font-normal">Status da fábrica</FormLabel>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="businessStatus"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center space-y-0 space-x-3">
										<FormControl>
											<Checkbox checked={field.value} onCheckedChange={field.onChange} className="focus:border-primary/50" />
										</FormControl>
										<FormLabel className="cursor-pointer text-sm font-normal">Status do comercial</FormLabel>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<div className="flex justify-end gap-3">
							<Button type="button" variant="ghost" onClick={handleClose}>
								<X className="size-4" />
								Cancelar
							</Button>
							<Button type="submit" disabled={isLoading} className="flex items-center gap-2">
								<Save className="size-4" />
								{isLoading ? "Salvando..." : "Salvar"}
							</Button>
						</div>
					</form>
				</Form>
			</div>
		</Modal>
	);
};
