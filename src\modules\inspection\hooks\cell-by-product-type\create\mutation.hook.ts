"use client";

import { useMutationFactory } from "@/shared/hooks/utils/mutation";
import { IMutationOptions, IMutationReturn } from "@/shared/hooks/utils/mutation/types";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { CELL_BY_PRODUCT_TYPE_ENDPOINTS } from "../../../api/endpoints";
import { INSPECTION_SUBJECTS } from "../../../constants/permissions/subjects";
import { inspectionKeys } from "../../../constants/query/keys";
import { ICreateCellByProductTypeDto } from "../../../types/cell-by-product-type/dtos/create.dto";

export const useCreateCellByProductTypeMutation = (params: IMutationOptions): IMutationReturn<IMessageGlobalReturn, ICreateCellByProductTypeDto> => {
	const mutation = useMutationFactory<IMessageGlobalReturn, ICreateCellByProductTypeDto>(
		{
			mutationKey: inspectionKeys.cellByProductType.custom("create"),
			endpoint: CELL_BY_PRODUCT_TYPE_ENDPOINTS.CREATE,
			subject: INSPECTION_SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_PRODUCT_TYPE,
			type: "create",
			messages: {
				loading: "Criando célula por tipo de produto...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			},
			queryKeys: inspectionKeys.cellByProductType,
		},
		{ ...params },
	);

	return { ...mutation };
};
