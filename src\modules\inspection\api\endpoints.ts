import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IPaginationParameters } from "@/shared/types/pagination/types";

const BASE = "/inspection";
const BASE_FORM = `${BASE}/forms`;
const BASE_FORM_LINK = `${BASE}/forms/links`;
const BASE_MEASURE = `${BASE}/measures`;
const BASE_FIELDS = `${BASE}/fields`;
const BASE_CELL_COMPONENTS = `${BASE}/production-cells-by-components`;
const BASE_CELL_BY_PRODUCT_TYPES = `${BASE}/production-cells-by-product-types`;
const BASE_COLLABORATORS_BY_SECTOR = `${BASE}/collaborators-by-sectors`;

const generateQueryParams = (base: string) => (params?: IPaginationParameters) => buildQueryParams(base, { ...params });

const makeCrud = (base: string, deleteBase?: string) =>
	Object.freeze({
		CREATE: base,
		FIND_ALL: generateQueryParams(base),
		DELETE: (id: string) => `${deleteBase ?? base}/${encodeURIComponent(id)}`,
	});

const makeForm = (base: string) => {
	const withId = (id: string) => `${base}/${encodeURIComponent(id)}`;
	return Object.freeze({
		CREATE: base,
		FIND_ALL: generateQueryParams(base),
		UPDATE: (id: string) => withId(id),
		DELETE: (id: string) => withId(id),
		CLONE: (id: string) => `${withId(id)}/clone`,
		FIND_BY_ID: (id: string) => withId(id),
	});
};

export const INSPECTION_FORM_ENDPOINTS = makeForm(BASE_FORM);
export const INSPECTION_FORMS_LINKS_ENDPOINTS = makeCrud(BASE_FORM_LINK);
export const MEASURES_ENDPOINTS = makeCrud(BASE_MEASURE);
export const FIELDS_ENDPOINTS = makeCrud(BASE_FIELDS);
export const CELL_COMPONENTS_ENDPOINTS = makeCrud(BASE_CELL_COMPONENTS);
export const CELL_BY_PRODUCT_TYPE_ENDPOINTS = makeCrud(BASE_CELL_BY_PRODUCT_TYPES);
export const COLLAB_BY_SECTOR_ENDPOINTS = Object.freeze({
	CREATE: BASE_COLLABORATORS_BY_SECTOR,
	FIND_ALL: generateQueryParams(BASE_COLLABORATORS_BY_SECTOR),
	DELETE: (id: string) => `${BASE_COLLABORATORS_BY_SECTOR}/${encodeURIComponent(id)}`,
	UPDATE: (id: string) => `${BASE_COLLABORATORS_BY_SECTOR}/${encodeURIComponent(id)}`,
	FIND_BY_ID: (id: string) => `${BASE_COLLABORATORS_BY_SECTOR}/find-one/${encodeURIComponent(id)}`,
});
