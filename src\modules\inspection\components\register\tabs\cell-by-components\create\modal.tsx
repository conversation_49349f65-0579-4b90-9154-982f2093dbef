import { Modal } from "../../../../../../../shared/components/custom/modal";
import { useCreateCellComponentForm } from "../../../../../hooks/cell-components/create/form.hook";
import { useCreateCellByComponentTypeMutation } from "../../../../../hooks/cell-components/create/mutation.hook";
import { TCreateCellComponentTypeForm } from "../../../../../validators/cell-components/create";
import { CreateInspectionCellComponentForm } from "./form";

interface IModalCreateCellComponentProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateCellComponent = ({ isOpen, onClose }: IModalCreateCellComponentProps) => {
	const methods = useCreateCellComponentForm();
	const handleClose = () => {
		onClose();
		methods.reset();
	};
	const { mutate } = useCreateCellByComponentTypeMutation({ onClose: handleClose });

	const handleSubmit = async (data: TCreateCellComponentTypeForm) => {
		await mutate({ productionCellId: data.cell?.id, componentId: data.component?.id });
	};

	return (
		<Modal isOpen={isOpen} onClose={handleClose} title="Cadastro de Célula por Componente">
			<CreateInspectionCellComponentForm methods={methods} onSubmit={handleSubmit} onClose={handleClose} />
		</Modal>
	);
};
