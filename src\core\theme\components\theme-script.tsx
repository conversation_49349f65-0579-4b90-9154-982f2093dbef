import { THEME_PERSISTENCE_CONFIG } from "../constants/preferences";

interface ThemeScriptProps {
	nonce?: string;
}

export function ThemeScript({ nonce }: ThemeScriptProps) {
	const themeScript = `"use strict";(function(){try{var CN='${THEME_PERSISTENCE_CONFIG.cookieName}';var VALID=['light','dark','light-green','dark-green'];function getCookie(n){if(typeof document==='undefined')return null;var v=('; '+document.cookie).split('; '+n+'=');if(v.length===2){var c=v.pop().split(';').shift();return c||null}return null}function isValid(t){return typeof t==='string'&&VALID.indexOf(t)>-1}function apply(t){for(var i=0;i<VALID.length;i++){document.documentElement.classList.remove(VALID[i])}document.documentElement.classList.add(t);document.documentElement.setAttribute('data-theme',t)}var theme='light';var raw=getCookie(CN);if(raw){try{raw=decodeURIComponent(raw)}catch(_){}if(raw&&raw.length<40){var val=raw; // aceita direto
if(val&&val[0]==='"'){try{val=JSON.parse(val)}catch(_){}}if(isValid(val))theme=val}}else if(typeof matchMedia==='function'){try{theme=matchMedia('(prefers-color-scheme: dark)').matches?'dark':'light'}catch(_){}}apply(theme)}catch(e){try{document.documentElement.classList.add('light');document.documentElement.setAttribute('data-theme','light')}catch(_){} }} )();`;

	return <script nonce={nonce} dangerouslySetInnerHTML={{ __html: themeScript }} />;
}
