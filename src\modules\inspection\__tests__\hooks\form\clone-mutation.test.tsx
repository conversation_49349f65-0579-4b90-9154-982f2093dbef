import { useCloneFormMutation } from "@/modules/inspection/hooks/form/clone/mutation.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";

jest.mock("@/shared/lib/requests", () => ({
	createPostRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canCreate: () => true })),
}));


const mockedCreatePostRequest = createPostRequest as jest.MockedFunction<typeof createPostRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Formulário clonado com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Error ao clonar o formulário" };

describe("useCloneFormMutation", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve clonar o formulário com sucesso", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useCloneFormMutation({}), { wrapper });
		await expect(result.current.mutate("123")).resolves.toEqual(mockSuccess);
	});

	it("deve falhar ao clonar o formulário", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useCloneFormMutation({}), { wrapper });
		await expect(result.current.mutate("123")).rejects.toThrow("Error ao clonar o formulário");
	});
});
