import { Modal } from "@/shared/components/custom/modal";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Trash } from "lucide-react";
import { useDeleteCellMutation } from "../../hooks/delete/mutation.hook";

interface DeleteCellModalProps {
	isOpen: boolean;
	onClose: () => void;
	id: string;
	name: string;
}

export const DeleteCellModal = ({ isOpen, onClose, id, name }: DeleteCellModalProps) => {
	const { mutate } = useDeleteCellMutation({ onClose });
	const handleConfirm = () => mutate(id);
	if (!id) return null;

	return (
		<Modal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
			<div className="flex flex-col items-center space-y-4 p-2 text-center">
				<Trash className="h-12 w-12 text-red-500" />
				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Deletar célula</h2>
					<p className="text-sm text-gray-600">
						Tem certeza que deseja excluir esta célula? <span className="text-primary font-medium">{name}</span>
					</p>
				</div>
				<div className="flex gap-3 pt-2">
					<Button variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button className="bg-red-400 hover:bg-red-500" onClick={handleConfirm}>
						Confirmar
					</Button>
				</div>
			</div>
		</Modal>
	);
};
