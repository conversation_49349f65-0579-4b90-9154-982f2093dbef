import { useDeleteCollabBySectorMutation } from "@/modules/inspection/hooks/collaborator-by-sector/delete/mutation.hook";
import { Trash } from "lucide-react";
import { Modal } from "../../../../../../../shared/components/custom/modal";
import { Button } from "../../../../../../../shared/components/shadcn/button";

interface IConfirmDeleteCollabBySectorTypeModal {
	id: string;
	isOpen: boolean;
	onClose: () => void;
	name?: string;
}

export const ConfirmDeleteCollabBySectorTypeModal = ({ id, isOpen, onClose, name }: IConfirmDeleteCollabBySectorTypeModal) => {
	const { mutate } = useDeleteCollabBySectorMutation({ onClose });

	const handleConfirm = () => mutate(id);

	return (
		<Modal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
			<div className="flex flex-col items-center space-y-4 p-2 text-center">
				<Trash className="h-12 w-12 text-red-500" />

				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Deletar Vinculo de colaboador por setor?</h2>
					<p className="text-muted-foreground">
						Você realmente deseja apagar o vinculo? <span className="text-primary font-medium">{name}</span>
					</p>
				</div>

				<div className="flex gap-3 pt-2">
					<Button variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button className="bg-red-400" onClick={handleConfirm}>
						Confirmar
					</Button>
				</div>
			</div>
		</Modal>
	);
};
